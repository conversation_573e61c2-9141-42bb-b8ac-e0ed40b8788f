name: Release and Deploy

on:
  push:
    tags:
      - "*"

permissions:
  contents: write

jobs:
  release:
    runs-on: ubuntu-latest
    env:
      NEXT_TELEMETRY_DISABLED: 1
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23.11
          cache: npm

      - name: Cache Next.js build artifacts (.next/cache)
        uses: actions/cache@v4
        with:
          path: ${{ github.workspace }}/.next/cache
          # Stable key across tags/commits for the same dependency set
          key: ${{ runner.os }}-next-cache-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-next-cache-

      - name: Install dependencies
        run: npm ci

      - name: Build static export
        run: npm run build

      - name: Verify build output
        run: |
          if [ ! -d out ]; then
            echo "Error: 'out/' directory not found after build." >&2
            exit 1
          fi

      - name: Archive static files (tar.gz)
        run: |
          cd out
          tar -czf ../madinamap-static-${GITHUB_REF_NAME}.tar.gz .
          cd -
          sha256sum madinamap-static-${GITHUB_REF_NAME}.tar.gz > madinamap-static-${GITHUB_REF_NAME}.tar.gz.sha256

      - name: Create GitHub Release and upload assets
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          name: ${{ github.ref_name }}
          draft: false
          prerelease: false
          make_latest: true
          generate_release_notes: true
          files: |
            madinamap-static-${{ github.ref_name }}.tar.gz
            madinamap-static-${{ github.ref_name }}.tar.gz.sha256
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  deploy:
    needs: release
    if: ${{ needs.release.result == 'success' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository (for deploy configs)
        uses: actions/checkout@v4

      - name: Download release asset (tar.gz)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release download "$GITHUB_REF_NAME" \
            -p "madinamap-static-${GITHUB_REF_NAME}.tar.gz" \
            -O "madinamap-static-${GITHUB_REF_NAME}.tar.gz"
          mkdir -p out
          tar -xzf "madinamap-static-${GITHUB_REF_NAME}.tar.gz" -C out

      - name: Set up SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add remote server to known_hosts
        run: |
          ssh-keyscan -H ${{ secrets.IP_ADDR }} >> ~/.ssh/known_hosts

      - name: Create remote directories
        env:
          IP_ADDR: ${{ secrets.IP_ADDR }}
          SSH_USER: ${{ secrets.SSH_USER }}
        run: |
          ssh "$SSH_USER@$IP_ADDR" 'mkdir -p ~/madinamap/out ~/madinamap/out_new ~/madinamap/deploy'

      - name: Upload build to staging and deploy configs to server
        env:
          IP_ADDR: ${{ secrets.IP_ADDR }}
          SSH_USER: ${{ secrets.SSH_USER }}
        run: |
          # Stage new build into out_new/ to minimize downtime
          rsync -avz --delete -e ssh ./out/ $SSH_USER@$IP_ADDR:madinamap/out_new/
          # Sync deploy configs
          rsync -avz -e ssh ./deploy/ $SSH_USER@$IP_ADDR:madinamap/deploy/

      - name: Run docker compose on server
        env:
          IP_ADDR: ${{ secrets.IP_ADDR }}
          SSH_USER: ${{ secrets.SSH_USER }}
        run: |
          ssh "$SSH_USER@$IP_ADDR" '
            set -e
            cd ~/madinamap
            # Fast final sync from staging into live dir and cleanup staging
            rsync -a --delete out_new/ out/
            rm -rf out_new/*
            # Apply any compose changes without forced recreation
            cd deploy && docker compose -f compose.yml up -d
          '
