services:
  caddy:
    image: caddy:2.10-alpine@sha256:a4180db0805b3725ddf936d2e6290553745c7339c003565da717ee612fd8a888
    container_name: madinamap
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ../out:/usr/share/caddy:ro
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
    read_only: true
    tmpfs:
      - /data
      - /config
    healthcheck:
      test: ["CMD", "caddy", "validate", "--config", "/etc/caddy/Caddyfile"]
      interval: 30s
      timeout: 5s
      retries: 3
