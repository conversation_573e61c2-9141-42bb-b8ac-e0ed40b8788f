# Caddy server for static Next.js export

:80 {
  root * /usr/share/caddy

  # Compression for text assets
  encode zstd gzip

  # Immutable cache for Next.js build assets
  @immutable {
    path /_next/static/*
  }
  header @immutable Cache-Control "public, max-age=31536000, immutable"

  # Cache common static assets for 30 days
  @static30d {
    path *.css *.js *.mjs *.png *.jpg *.jpeg *.gif *.svg *.ico *.webp *.avif *.ttf *.otf *.woff *.woff2
  }
  header @static30d Cache-Control "public, max-age=2592000"

  # Clean URLs: /map -> /map.html, also supports /dir -> /dir/index.html
  try_files {path} {path}/ {path}.html /404.html

  file_server
}

