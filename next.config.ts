import createNextIntlPlugin from 'next-intl/plugin';
import type { NextConfig } from "next";

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  // Generate a fully static export
  output: 'export',
  compiler: {
    styledComponents: true,
  },
  images: {
    unoptimized: true,
  },

  eslint: {
    ignoreDuringBuilds: true, // skips all ESLint errors at build time
  },

  // Webpack configuration for ArcGIS
  webpack: (config, { isServer }) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      dns: false,
      child_process: false,
      module: false,
    };

    // Add this for ArcGIS
    if (!isServer) {
      config.resolve.alias['@arcgis/core/assets'] = require.resolve('@arcgis/core/assets');
    }

    return config;
  },

  // Required for ArcGIS
  serverExternalPackages: ['@arcgis/core'],
  
};

export default withNextIntl(nextConfig);
