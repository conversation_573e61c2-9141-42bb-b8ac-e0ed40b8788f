# MadinaMap - Interactive City Map

Interactive map for commercial signage regulations in Medina

## 📋 Overview

An interactive application that reflects the regulations for commercial signage in Medina. Our goal is to achieve the optimal level of integration between commercial signs and building facades, considering the different architectural designs from one urban area to another.

## 🚀 Technologies Used

- **Next.js 15** - React framework with App Router
- **React 19** - User interface library
- **TypeScript** - Programming language with static types
- **Tailwind CSS 4** - CSS framework for design
- **next-intl** - Internationalization and translation system
- **Lucide React** - Icon library
- **Turbopack** - Fast build tool

## 🏗️ Project Structure

```
src/
├── app/                    # App Router - Application pages
│   ├── layout.tsx         # Main layout
│   ├── page.tsx          # Home page
│   ├── not-found.tsx     # 404 page
│   └── globals.css       # Global styles
├── components/            # Reusable components
│   ├── atoms/            # Basic components
│   ├── molecules/        # Medium components
│   └── organisms/        # Complex components
├── data/                 # Data and translations
│   ├── ar/              # Arabic translations
│   └── homePage.ts      # Home page data
├── hooks/               # Custom React Hooks
├── i18n/               # Internationalization settings
├── lib/                # Libraries and types
└── utils/              # Helper functions
```

## 🎯 Key Features

### 🏠 Home Page
- Hero section with title and description
- Display of three main services
- Urban classification of areas
- Commercial signage gallery
- Products and lighting section

### 🧭 Navigation
- Fixed navigation bar with logo
- Smooth navigation between sections
- Responsive mobile menu
- Automatic active section tracking

### 📱 Responsive Design
- Full mobile device support
- Responsive design for all screen sizes
- Modern and consistent user interface

### 🌐 Internationalization
- Arabic language support
- RTL (Right-to-Left) layout
- Custom Arabic fonts
- Comprehensive content translations

### 📞 Contact Form
- Interactive contact form
- Data validation
- Detailed error messages
- Multiple submission states

### ❓ FAQ Section
- Interactive Accordion system
- Support for multiple content types:
  - Simple text
  - Bullet lists
  - Mixed content (text + list)

## 🛠️ Installation and Setup

### Prerequisites
- Node.js 18.17 or newer
- npm, yarn, pnpm, or bun

### Installation Steps

1. **Clone the project**
```bash
git clone [repository-url]
cd madinamap
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

3. **Run development server**
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

4. **Open the application**
Open [http://localhost:3000](http://localhost:3000) in your browser

## 📜 Available Commands

```bash
npm run dev      # Run development server with Turbopack
npm run build    # Build application for production
npm run start    # Run application in production mode
npm run lint     # Check code using ESLint
```

## 🎨 Design System

### Main Colors
- **Primary**: Main brand green
- **Secondary**: Secondary colors for distinction
- **Background**: Gradient and varied backgrounds

### Fonts
- **TheSansArabic**: Main font for Arabic texts
- **El Messiri**: Fallback font from Google Fonts

### Components
- **Atomic Design** pattern: Atoms → Molecules → Organisms
- Reusable components
- Clearly defined Props with TypeScript

## 🔧 Configuration

### next.config.ts
```typescript
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  compiler: {
    styledComponents: true,
  },
  images: {
    unoptimized: true,
  },
};

export default withNextIntl(nextConfig);
```

### Internationalization
- next-intl setup for Arabic language only
- No language prefix in URLs
- Full RTL support

## 📁 Adding New Content

### Adding a New Page
```typescript
// src/app/new-page/page.tsx
export default function NewPage() {
  return (
    <div>
      <h1>New Page</h1>
    </div>
  );
}
```

### Adding a New Component
```typescript
// src/components/atoms/NewComponent/index.tsx
interface NewComponentProps {
  title: string;
  description?: string;
}

export default function NewComponent({ title, description }: NewComponentProps) {
  return (
    <div>
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  );
}
```

### Adding New Translations
```typescript
// src/data/ar/index.ts
const ar = {
  // ... existing translations
  "newSection": {
    "title": "New Title",
    "description": "New Description"
  }
};
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect project to Vercel
2. Automatic deployment on every push
3. Instant preview of changes

### Local Build
```bash
npm run build
npm run start
```