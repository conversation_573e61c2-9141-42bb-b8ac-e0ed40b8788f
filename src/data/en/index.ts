const en = {
    "hero": {
        "title1": 'الخريطة التفاعلية لاشتراطات اللوحات التجارية بالمدينة المنورة',
        "intro": 'تطبيق تفاعلي يعكس الاشتراطات الخاصة باللوحات التجارية بالمدينة المنورة, هدفنا هو الوصول بالمستوي الأمثل لتكامل اللوحات التجارية مع واجهات المباني مع اختلاف التصماميم المعمارية من منطقة حضارية لأخرى.'
    },
    "Common": {
        "getService": "الدخول للدخدمة"
    },
    "404PageData": {
        "title": "404 - Page Not Found",
        "intro": "Sorry, the page you are looking for does not exist.",
        "button": "Go back home"
    },
    "urbanServicesCommercial": {
        "1": {
            "title": "Main Commercial Axis 1",
        },
        "2": {
            "title": "Main Commercial Axis 2",
        },
        "3": {
            "title": "Sub Commercial Axis",
        },
        "4": {
            "title": "Quba Avenue",
        },
    },
    "urbanServicesZones": {
        "1": {
            "title": "Commercial Zones",
        },
        "2": {
            "title": "Residential Zones",
        },
        "3": {
            "title": "Central Zones",
        },
        "4": {
            "title": "Farms and Rest Areas",
        },
        "5": {
            "title": "Workshops and Storage Areas",
        },
        "6": {
            "title": "Special Nature Zones",
        }
    },
};

export default en;