// Configurable panels catalog (temporary 8 panels as per requirement)
export type PanelItem = { id: number; title: string; imgSrc: string };

export const BASE_PANELS: PanelItem[] = [
    { id: 1, title: "اللوحة الموازية", imgSrc: "/images/panel1.png" },
    { id: 2, title: "اللوحة المظلة", imgSrc: "/images/panel4.png" },
    { id: 3, title: "لوحة نافذة العرض", imgSrc: "/images/panel5.png" },
    { id: 4, title: "لوحة العلامة التجارية", imgSrc: "/images/panel7.png" },
    { id: 5, title: "اللوحة العمودية", imgSrc: "/images/panel3.png" },
    { id: 6, title: "لوحة المستأجرين للأدوار العليا", imgSrc: "/images/panel8.png" },
    // { id: 5, title: "لوحات الأدوار المتكررة", imgSrc: "/images/panel5.png" },
];