// Data for navigation links

import { NavigationItem, ProductAndLightCardProps, ServiceCardProps } from "@/lib/types";

// navigation.ts
export const navigationItems: NavigationItem[] = [
    { id: 'hero', label: 'الرئيسية', onClick: () => { } },
    { id: 'services', label: 'الخدمات', onClick: () => { } },
    { id: 'urban-classification', label: 'التصنيف الحضري', onClick: () => { } },
    { id: 'panels', label: 'اللوحات', onClick: () => { } },
    { id: 'products', label: 'المنتجات', onClick: () => { } },
    { id: 'lighting', label: 'الإضاءة', onClick: () => { } },
    { id: 'contact', label: 'التواصل', onClick: () => { } },
];


// Data for service section
export const services: ServiceCardProps[] = [
    {
        id: 1,
        title: 'خريطة الإشتراطات',
        description: 'استخدم هذه الخدمة لتحديد موقع قطعة الأرض ومعرفة الاشتراطات الخاصة باللوحات التجارية في نطاقها، عبر خريطة تفاعلية سهلة الاستخدام.',
        image: "/images/mapImage.jpg",
        link: '/map'
    },
    {
        id: 2,
        title: 'الدليل التنظيمي',
        description: 'إطّلع على الدليل الشامل الذي يشرح معايير اللوحات التجارية، بما يشمل: المقاسات، المواقع المسموحة، الأنماط المعمارية، والخامات المقبولة.',
        image: "/images/mapImage2.png"
    },
    {
        id: 3,
        title: 'نماذج اللوحات',
        description: 'استعرض أمثلة للوحات المسموح بها وغير المسموح بها، مع توضيحات مرئية تساعدك على فهم تطبيق الاشتراطات بشكل عملي وسريع.',
        image: "/images/mapImage3.png"
    }
];

// Data for UrbanClassification section
// urbanServices data for Commercial Axes
export const urbanServicesCommercial: ServiceCardProps[] = [
    {
        id: 1,
        title: 'Main Commercial Axis 1',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/urban1.jpg",
    },
    {
        id: 2,
        title: 'Main Commercial Axis 2',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/urban2.jpg"
    },
    {
        id: 3,
        title: 'Sub Commercial Axis',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/urban3.jpg"
    },
    {
        id: 4,
        title: 'Quba Avenue',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/urban4.jpg"
    }
];

// urbanServices data for zones
export const urbanServicesZones: ServiceCardProps[] = [
    {
        id: 1,
        title: 'Commercial Zones',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone1.jpg",
    },
    {
        id: 2,
        title: 'Residential Zones',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone2.jpg"
    },
    {
        id: 3,
        title: 'Central Zones',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone3.jpg"
    },
    {
        id: 4,
        title: 'Farms and Rest Areas',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone4.jpg"
    },
    {
        id: 5,
        title: 'Workshops and Storage Areas',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone5.jpg"
    },
    {
        id: 6,
        title: 'Special Nature Zones',
        description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
        image: "/images/zone6.jpg"
    }
];

// Data for Panels section
export const PanelsData = [
    {
        id: 1,
        cardNumber: "01",
        title: "لوحة العلامة التجارية",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel7.png'
    },
    {
        id: 2,
        cardNumber: "02",
        title: "لوحة نافذة العرض",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel5.png'
    },
    {
        id: 3,
        cardNumber: "03",
        title: "اللوحة المظلة",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel4.png'
    },
    {
        id: 4,
        cardNumber: "04",
        title: "اللوحة العمودية",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel3.png'
    },
    {
        id: 5,
        cardNumber: "05",
        title: "اللوحة الموازية",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel1.png'
    },
    {
        id: 6,
        cardNumber: "06",
        title: "لوحة المستأجرين للأدوار العليا",
        description: "اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمودية على البناء الذي يُعلق عليه المظلة.",
        purpose: "من المفترض أن نقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك لكون الحجم محدود وغير موجه لحركة مرور السيارات.",
        generalRules: [
            "قواعد عامة:اللوحات المعلقة من المظلة يجب أن تكون معتمدة بالعرض ويجب ألا تسد الممشات أو ممر المشاة.",
            "الوحدات المسموح بها لكل نشاط تجاري.",
            "يُسمح لكل نشاط تجاري بأن يكون له وحدة واحدة على واجهة. الحد الأدنى للمسافة بين الوحدتين هو 5 متر."
        ],
        colors: [
            "تتوقف على طراز المنطقة."
        ],
        lighting: [
            "تتوقف على التنسيق الحضري."
        ],
        imageURL:'/images/panel8.png'
    },
];

// Data for lighting section
export const lightingData: ProductAndLightCardProps[] = [
    {
        id: 1,
        title: 'الإضاءة من الخلف',
        image: "/images/lightImage1.png"
    },
    {
        id: 2,
        title: 'الإضاءة من الأمام',
        image: "/images/lightImage2.png"
    },
    {
        id: 3,
        title: 'الإضاءة من الجانب',
        image: "/images/lightImage3.png"
    },
    {
        id: 4,
        title: 'إضائة النيون',
        image: "/images/lightImage4.png"
    }
];


// Data for Products section
export const ProductsData: ProductAndLightCardProps[] = [
    {
        id: 1,
        title: 'لوحة مسطحة',
        image: "/images/productImg1.png"
    },
    {
        id: 2,
        title: 'حروف ثلاثية الابعاد',
        image: "/images/productImg2.png"
    },
    {
        id: 3,
        title: 'صندوق مضيء',
        image: "/images/productImg3.png"
    },
];

// data for urbanexample Images
export const exampleImages = [
    { src: "/images/example2.png", alt: "example2", width: 500, height: 300 },
    { src: "/images/example3.png", alt: "example2", width: 500, height: 300 },
    { src: "/images/mapImage3.png", alt: "example3", width: 500, height: 300 },
    { src: "/images/example4.png", alt: "example4", width: 500, height: 300 },
];

//Data for  permittedSigns section in UrbanClassification page
export const permittedSignsData = [
    { heading: 'لوحة الموازية لسطح الواجهة', title: 'الشروط الخاصة', description: '1/3 ارتفاع واجهة المحل بدون حد اقصى' },
    { heading: 'لوحة العلامة التجارية' },
    { heading: 'اللوحة المتعامدة على سطح الواجهة' },
    { heading: 'بوحة نافذة العرض', title: 'الشروط الخاصة', description: 'لا يتجاوز حجمها 50% من نافذة العرض وبحد أقصى 6م2' },
    { heading: 'لوحات المستأجرين لأدوار علوية', title: 'الشروط الخاصة', description: '1/3 ارتفاع واجهة المحل بدون حد اقصى' },
    { heading: 'لوحة المظلة' },
]

// Data for Products section in UrbanClassification page
export const ProductsDataUrban: ProductAndLightCardProps[] = [
    {
        id: 1,
        title: 'لوحة مسطحة',
        image: "/images/productImg7.png"
    },
    {
        id: 2,
        title: 'حروف ثلاثية الابعاد',
        image: "/images/productImg8.png"
    },
    {
        id: 3,
        title: 'صندوق مضيء',
        image: "/images/productImg6.png"
    }
];

// Data for lighting section in UrbanClassification page
export const lightingDataUrban: ProductAndLightCardProps[] = [{
    id: 1,
    title: 'الإضاءة من الخلف',
    image: "/images/lightImage1.png"
},
{
    id: 2,
    title: 'الإضاءة من الأمام',
    image: "/images/lightImage2.png"
},
{
    id: 3,
    title: 'الإضاءة من الجانب',
    image: "/images/lightImage3.png"
}];