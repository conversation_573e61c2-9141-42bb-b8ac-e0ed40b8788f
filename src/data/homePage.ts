// Data for navigation links

import { NavigationItem, ProductAndLightCardProps, ServiceCardProps } from "@/lib/types";

// navigation.ts
export const navigationItems: NavigationItem[] = [
  { id: 'hero', label: 'الرئيسية', onClick: () => { } },
  { id: 'services', label: 'الخدمات', onClick: () => { } },
  { id: 'urban-classification', label: 'التصنيف الحضري', onClick: () => { } },
  { id: 'panels', label: 'اللوحات', onClick: () => { } },
  { id: 'products', label: 'المنتجات', onClick: () => { } },
  { id: 'lighting', label: 'الإضاءة', onClick: () => { } },
  { id: 'contact', label: 'التواصل', onClick: () => { } },
];


// Data for service section
export const services: ServiceCardProps[] = [
  {
    id: 1,
    title: 'خريطة الإشتراطات',
    description: 'استخدم هذه الخدمة لتحديد موقع قطعة الأرض ومعرفة الاشتراطات الخاصة باللوحات التجارية في نطاقها، عبر خريطة تفاعلية سهلة الاستخدام.',
    image: "/images/mapImage.jpg",
    link: '/map'
  },
  {
    id: 2,
    title: 'الدليل التنظيمي',
    description: 'إطّلع على الدليل الشامل الذي يشرح معايير اللوحات التجارية، بما يشمل: المقاسات، المواقع المسموحة، الأنماط المعمارية، والخامات المقبولة.',
    image: "/images/mapImage2.png"
  },
  {
    id: 3,
    title: 'نماذج اللوحات',
    description: 'استعرض أمثلة للوحات المسموح بها وغير المسموح بها، مع توضيحات مرئية تساعدك على فهم تطبيق الاشتراطات بشكل عملي وسريع.',
    image: "/images/mapImage3.png"
  }
];

// Data for UrbanClassification section
// urbanServices data for Commercial Axes
export const urbanServicesCommercial: ServiceCardProps[] = [
  {
    id: 1,
    classification: "محاور تجارية رئيسية 1",
    title: 'Main Commercial Axis 1',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/urban1.jpg",
    intro: {
      title: "محاور تجارية رئيسية 1",
      description:
        "هي المحاور الرئيسية لمداخل المدينة المنورة والطرق الدائرية الرئيسية بالدرجة الأولى المنتهية بصريًا بشكل مباشر مع المسجد النبوي الشريف، كما أنها تربط بين بلديات المدينة المنورة ببعضها وتنتشر عليها المراكز التجارية الكبرى ذات التردد العالي للزوار.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.8 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 1.2 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.8 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: الحد الاقصي 0.2 م",
      },
      {
        heading: "لوحة المظلة",
        title: "الشروط الخاصة",
        description: "ارتفاع الكتابة لا يزيد عن 0.60 سم",
      },
      {
        heading: "لوحة نافذة العرض",
        title: "الشروط الخاصة",
        description:
          "المساحة: 25% من مساحة النافذة بما لا يزيد عن 4 م",
      },
      {
        heading: "لوحة المستأجرين للادوار العليا",
        title: "الشروط الخاصة",
        description: "الارتفاع من الارض: الحد الاقصي 0.5 م - العرض: الحد الاقصي 1.0 م - الارتفاع: الاحد الاقصي 2.0 م",
      },

      {
        heading: "العلامة التجارية",
        title: "الشروط الخاصة",
        description: "حسب الطابع المعماري للمبنى والتصميم المقدم.",
      },
    ],
    exampleImages: [
      {
        src: "/images/Comm_axis_1_ex1.png",
        alt: "محاور تجارية رئيسية 1 - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Comm_axis_1_ex2.png",
        alt: "محاور تجارية رئيسية 1 - مثال 2",
        height: 100,
        width: 100
      }
    ]
  },
  {
    id: 2,
    classification: "محاور تجارية رئيسية 2",

    title: 'Main Commercial Axis 2',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/urban2.jpg",
    intro: {
      title: "محاور تجارية رئيسية 2",
      description:
        "هي محاور رئيسية تربط بين أحياء المدينة المنورة وتمتاز بتركز وتنوع الأنشطة التجارية فيها من محلات البيع والمطاعم والمقاهي وتنتشر فيها الأنشطة الإدارية.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.6 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 1.0 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.6 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: الحد الأقصى 0.2 م",
      },
      {
        heading: "لوحة المظلة",
        title: "الشروط الخاصة",
        description: "ارتفاع الكتابة لا يزيد عن 0.60 سم",
      },
      {
        heading: "لوحة نافذة العرض",
        title: "الشروط الخاصة",
        description:
          "المساحة: 25% من مساحة النافذة بما لا يزيد عن 2 م2",
      },
      {
        heading: "لوحة المستأجرين للادوار العليا",
        title: "الشروط الخاصة",
        description: "الارتفاع من الارض: الحد الاقصي 0.5 م - العرض: الحد الاقصي 0.8 م - الارتفاع: الاحد الاقصي 1.5 م",
      },

      {
        heading: "العلامة التجارية",
        title: "الشروط الخاصة",
        description: "حسب الطابع المعماري للمبنى والتصميم المقدم.",
      },
    ],
    exampleImages: [
      {
        src: "/images/Comm_axis_2_ex1.png",
        alt: "محاور تجارية رئيسية 2 - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Comm_axis_2_ex2.png",
        alt: "محاور تجارية رئيسية 2 - مثال 2",
        height: 100,
        width: 100
      }
    ]
  },
  {
    id: 3,
    classification: "محاور تجارية فرعية",
    title: 'Sub Commercial Axis',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/urban3.jpg",
    intro: {
      title: "محاور تجارية فرعية",
      description:
        "هي المستوى الأخير من المحاور التجارية وتخدم الأحياء والمناطق السكنية المختلفة. تتميّز هذه المحاور بتنوّع الأنشطة التجارية، حيث تضم المحلات التجارية الصغيرة، والمطاعم، والمقاهي، بالإضافة إلى المباني الخدمية.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.8 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "لوحة المظلة",
        title: "الشروط الخاصة",
        description: "ارتفاع الكتابة لا يزيد عن 0.60 سم",
      },
      {
        heading: "لوحة نافذة العرض",
        title: "الشروط الخاصة",
        description:
          "المساحة: 25% من مساحة النافذة بما لا يزيد عن 2 م2",
      },
      {
        heading: "العلامة التجارية",
        title: "الشروط الخاصة",
        description: "حسب الطابع المعماري للمبنى والتصميم المقدم",
      },
    ],
    exampleImages: [
      {
        src: "/images/Sub_axis_ex1.png",
        alt: "محاور تجارية فرعية - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Sub_axis_ex2.png",
        alt: "محاور تجارية فرعية - مثال 2",
        height: 100,
        width: 100
      }
    ],
    lightingDataUrban: [{
      id: 1,
      title: 'الإضاءة من الخلف',
      image: "/images/lightImage1.png"
    },
    {
      id: 2,
      title: 'الإضاءة من الجانب',
      image: "/images/lightImage3.png"
    }]

  },
  {
    id: 4,
    title: 'Quba Avenue',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/urban4.jpg",
    intro: {
      title: "جادة قباء",
      description:
        "مسار مشاة يتميّز بأهميته الثقافية والدينية، مخصص للمشاة ويصل بين المنطقة المركزية ومسجد قباء بامتداد يصل إلى 2.80 كم تقريبًا، ويُعد وجهة مهمة لزوار وسكان المدينة المنورة.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.8 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: لا تزيد المسافة بين اللوحة وواجهة المبنى عن 20 سم",
      },
      {
        heading: "لوحة نافذة العرض",
        title: "الشروط الخاصة",
        description:
          "المساحة: 40% من مساحة النافذة بما لا يزيد عن 2 م2",
      },
    ],
    exampleImages: [
      {
        src: "/images/Quba_axis_ex1.png",
        alt: "جادة قباء - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Quba_axis_ex2.png",
        alt: "جادة قباء - مثال 2",
        height: 100,
        width: 100
      }
    ],
  }
];

// urbanServices data for zones
export const urbanServicesZones: ServiceCardProps[] = [
  {
    id: 1,
    classification: "مناطق تجارية",
    title: 'Commercial Zones',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/zone1.jpg",
    intro: {
      title: "المناطق التجارية",
      description: "تضم المدينة المنورة مجموعة من المناطق التجارية المتنوعة، تتوزع بين المراكز التجارية الحديثة والمولات الخطية المفتوحة التي تمتد على المحاور رئيسية إلى جانب التجمعات التجارية المجمعة"
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.8 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: لا تزيد المسافة بين اللوحة وواجهة المبنى عن 20 سم",
      },
      {
        heading: "لوحة نافذة العرض",
        title: "الشروط الخاصة",
        description:
          "المساحة: 25% من مساحة النافذة بما لا يزيد عن 2 م2",
      },
      {
        heading: "لوحة المظلة",
        title: "الشروط الخاصة",
        description: "ارتفاع الكتابة لا يزيد عن 0.60 سم",
      },
    ],
    exampleImages: [
      {
        src: "/images/Comm_zone_ex1.png",
        alt: "المناطق التجارية - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Comm_zone_ex2.png",
        alt: "المناطق التجارية - مثال 2",
        height: 100,
        width: 100
      }

    ]
  },
  {
    id: 2,
    classification: "مناطق سكنية",
    title: 'Residential Zones',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/zone2.jpg",
    intro: {
      title: "المناطق السكنية",
      description:
        "هي مناطق الأحياء السكنية التي يتواجد بها الفيلات والعمارات، والتي قد تشمل مجموعة من الأنشطة التجارية والخدمات مثل (المشاغل – البقالات الصغيرة – مراكز التحفيظ – المغاسل ... إلخ).",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.8 م - البروز: الحد الأقصى 0.2 م",
      },
    ],
    exampleImages: [
      {
        src: "/images/Res_zone_ex1.jpg",
        alt: "المناطق السكنية - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Res_zone_ex2.jpg",
        alt: "المناطق السكنية - مثال 2",
        height: 100,
        width: 100
      }
    ],
    productsData: [
      {
        id: 1,
        title: 'لوحة مسطحة',
        image: "/images/productImg7.png"
      },
      {
        id: 2,
        title: 'حروف ثلاثية الابعاد',
        image: "/images/productImg8.png"
      }
    ],
    lightingDataUrban: [{
      id: 1,
      title: 'الإضاءة من الخلف',
      image: "/images/lightImage1.png"
    }]
  },

  {
    id: 3,
    classification: "مناطق مركزية",
    title: 'Central Zones',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/zone3.jpg",
    intro: {
      title: "المنطقة المركزية (منطقة الحرم)",
      description:
        "هي قلب المدينة، وتُعدّ الأكثر حيوية وكثافة، كما أنها المقصد الرئيسي للزوار، وتركّز بشكل كبير على المستخدمين من المشاة، ويُعدّ الاستعمال الغالب في المنطقة فندقيًا وتجاريًا.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: لا تزيد المسافة بين اللوحة وواجهة المبنى عن 20 سم",
      },
      {
        heading: "العلامة التجارية",
        title: "الشروط الخاصة",
        description: "حسب الطابع المعماري للمبنى والتصميم المقدم.",
      },
    ],
    exampleImages: [
      {
        src: "/images/Cent_zone_ex1.jpg",
        alt: "المنطقة المركزية - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Cent_zone_ex2.png",
        alt: "المنطقة المركزية - مثال 2",
        height: 100,
        width: 100
      }
    ],

  },
  {
    id: 4,
    classification: "مناطق المزارع والإستراحات",

    title: 'Farms and Rest Areas',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/zone4.jpg",
    intro: {
      title: "مناطق المزارع والإستراحات",
      description:
        "تنتشر مناطق الإستراحات والمزارع ضمن تجمعات موزعة في أرجاء المدينة، وتتميز هذه التجمعات بطابعها الريفي، وتُستخدم للترفيه والاستجمام، وأنشطة الزراعة. يطغى على هذه المناطق البناء الأفقي البسيط، والمساحات الخضراء المفتوحة.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 0.8 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: لا تزيد المسافة بين اللوحة وواجهة المبنى عن 20 سم",
      },
    ],
    exampleImages: [
      {
        src: "/images/Farms_zone_ex1.jpg",
        alt: "مناطق المزارع والاستراحات - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Farms_zone_ex2.jpg",
        alt: "مناطق المزارع والاستراحات - مثال 2",
        height: 100,
        width: 100
      }
    ],
    productsData: [
      {
        id: 1,
        title: 'لوحة مسطحة',
        image: "/images/productImg7.png"
      },
      {
        id: 2,
        title: 'حروف ثلاثية الابعاد',
        image: "/images/productImg8.png"
      }
    ],
    lightingDataUrban: [{
      id: 1,
      title: 'الإضاءة من الخلف',
      image: "/images/lightImage1.png"
    }]
  },

  {
    id: 5,
    classification: "مناطق الورش والمستودعات",
    title: 'Workshops and Storage Areas',
    description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
    image: "/images/zone5.jpg",
    intro: {
      title: "مناطق الورش والمستودعات",
      description:
        "تتخصص مناطق الورش والمستودعات بالأنشطة الإنتاجية والتخزينية، حيث تحتوي على مبانٍ منخفضة الارتفاع ومتوسطة الحجم. تقع هذه المناطق في مواقع يسهل الوصول إليها من الطرق الرئيسية، ما يُسهل حركة المركبات الثقيلة والشاحنات.",
    },
    permittedSignsData: [
      {
        heading: "اللوحة الموازية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - العرض: بطول واجهة المحل - الارتفاع: الحد الأقصى 1.2 م - البروز: الحد الأقصى 0.2 م",
      },
      {
        heading: "اللوحة العمودية",
        title: "الشروط الخاصة",
        description:
          "الارتفاع من الأرض: الحد الأدنى 2.4 م - الطول: الحد الأقصى 0.6 م - الارتفاع: الحد الأقصى 0.6 م - البروز: الحد الأقصى 0.8 م - العمق: لا تزيد المسافة بين اللوحة وواجهة المبنى عن 20 سم",
      },
      {
        heading: "العلامة التجارية",
        title: "الشروط الخاصة",
        description: "حسب الطابع المعماري للمبنى والتصميم المقدم.",
      },
    ],
    exampleImages: [
      {
        src: "/images/Workshops_zone_ex1.jpg",
        alt: "مناطق الورش والمستودعات - مثال 1",
        height: 100,
        width: 100
      },
      {
        src: "/images/Workshops_zone_ex2.jpg",
        alt: "مناطق الورش والمستودعات - مثال 2",
        height: 100,
        width: 100
      }
    ],
  },
  // {
  //   id: 6,
  //   title: 'Special Nature Zones',
  //   description: 'تتطلــب المناطــق التجاريــة تنوعــاً كبــراً في أنــواع اللوحــات، وكل الواجهــات المنظــورة مــن الفضــاء العـام (سـواء كانـت طرقـا عامـة أو فضـاء مفتـوح) .',
  //   image: "/images/zone6.jpg"
  // }
];

// Data for Panels section
export const PanelsData = [
  {
    id: 1,
    cardNumber: "01",
    title: "اللوحة الموازية",
    description:
      "هي لوحة يتم تثبيتها مباشرة على الواجهة الأمامية للمبنى، بحيث يكون امتدادها متماشيا مع مستوى الحائط. تكون أسفل مستوى الدور الأول وتُستخدم لتوضيح اسم المتجر.",
    purpose: "",
    generalRules: [
      "يمنع إخفاء أية عناصر معمارية للواجهة.",
      "يمنع تركيب اللوحات فوق منسوب الطابق الأرضي.",
      "يمنع استخدام عناصر إنشائية إضافية لتثبيت اللوحات.",
      "يمنع تركيب الأعمدة الحديدية خارج الموقع المخصص للوحة.",
      "يمنع إنشاء صناديق الجبس لتركيب اللوحة.",
      "لا يسمح بوجود تباين في مستوى اللوحات وارتفاعها على مستوى المبنى الواحد؛ ويلزم توحيد ارتفاع اللوحات على نفس المبنى بحيث تكون جميع لوحات المحلات على المبنى الواحد بذات الأبعاد من حيث مستوى التركيب وارتفاع اللوحة.",
      "بالمنطقة المركزية: غير مسموح بتركيب اللوحات خارج الرواق.",
      "بالمنطقة المركزية: يسمح بوضع اللوحة على التشطيب النهائي أعلى واجهة المحل من داخل الرواق فقط مع التزام جميع المحلات على نفس المبنى بنفس الموقع."
    ],
    regulations: [
      {
        name: "العلاقة بين اللوحات والعناصر المعمارية للمبنى",
        regulation: {
          "1": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_1.png", description: "يمنع إخفاء أية عناصر معمارية للواجهة." },
              { imgSrc: "/images/para_panel_reg_2.png", description: "يمنع تركيب اللوحات فوق منسوب الطابق الأرضي." }
            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_3.png", description: "" }
            ]
          },
          "يمنع استخدام عناصر إنشائية إضافية لتثبيت اللوحات": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_4.png", description: "يمنع تركيب الأعمدة الحديدية خارج الموقع المخصص للوحة." },
              { imgSrc: "/images/para_panel_reg_5.png", description: "يمنع إنشاء صناديق الجبس لتركيب اللوحة." },
            ],
            allowed: [

            ]
          },
          "3": {
            notAllowed: [

              { imgSrc: "/images/para_panel_reg_6.png", description: "لا يسمح بوجود تباين في مستوى اللوحات وارتفاعها على مستوى المبنى الواحد." }
            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_7.png", description: "يلزم أن يتم توحيد إرتفاع اللوحات الموازية لسطح الواجهة  على المبنى الواحد بحيث تكون جميع لوحات المحلات على المبنى الواحد بذات الأبعاد من حيث مستوى التركيب وإرتفاع اللوحة" }
            ]
          },
          "4": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_8.png", description: "يحظر تركيب اللوحات بشكل متجاور دون ترك مسافة فاصلة مناسبة" },

            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_9.png", description: "يلزم ترك مسافة 10 سم كحد أدنى من جهة اللوحة المجاورة لكل متجر." }
            ]
          },
          "في حالة امتلاك نفس المؤسسة أكثر من نافذة أو متجر ": {
            description: "في حالة امتلاك نفس المؤسسة اكثر من نافذة أو متجر فمن الممكن وضع لوحة واحدة متصلة عليهم، وفي تلك الحالة يمكن كتابة اسم المتجر أعلى كل مدخل أو كل 7م بحد أقصى 3 مرات على الواجهة الواحدة",
            notAllowed: [],
            allowed: [
              { imgSrc: "/images/para_panel_reg_10.png", description: "لوحة منفصلة في حالة وجود عناصر معمارية " },
              { imgSrc: "/images/para_panel_reg_11.png", description: "لوحة متصلة في حالة عدم وجود عناصر معمارية  " },
            ]
          },
          "اللوحات في حالة المباني الزاوية": {
            description: "إذا كان المبنى على زاوية مع وجود مدخل أو نوافذ على كلا الجانبين فيمكن وضع اللوحة على الزاوية.",
            notAllowed: [],
            allowed: [
              { imgSrc: "/images/para_panel_reg_12.png", description: "" }
            ]
          },
        }
      },
      {
        name: "اللوحات في المباني ذات الأروقة",
        regulation: {
          "1": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_13.png", description: "غير مسموح بإختلاف مكان تركيب اللوحات سواءاً داخل الرواق أو خارجه على الواجهة الواحدة." }
            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_14.png", description: "يلزم توحيد مكان تركيب اللوحات سواءاً داخل الرواق أو خارجه على الواجهة الواحدة." },
            ]
          },
          "2": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_15.png", description: "يمنع تجاوز اللوحة نهاية العمود وتغطية أقواس الرواق" }
            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_16.png", description: "يسمح بوضع اللوحة بين أعمدة الرواق على الواجهة الخارجية للمبنى،مع إلزام جميع المحلات على نفس المبنى بنفس الموقع، وفي هذه الحالة يلزم ألا يزيد طول اللوحة عن 60 سم." },
            ]
          }
        }
      },
      {
        name: "اللوحات في المباني ذات الأروقة بالمنطقة المركزية",
        regulation: {
          "1": {
            notAllowed: [
              { imgSrc: "/images/para_panel_reg_17.png", description: "غير مسموح بتركيب اللوحات خارج الرواق بالمنطقة المركزية." }
            ],
            allowed: [
              { imgSrc: "/images/para_panel_reg_18.png", description: "يسمح بوضع اللوحة على التشطيب النهائي أعلى واجهة المحل من داخل الرواق (فقط) مع التزام جميع المحلات على نفس المبنى بنفس الموقع." }
            ]
          }
        }
      }
    ],
    standardSpecs: {
      "الارتفاع عن الأرض": "الحد الأدنى 2.4 م من منسوب الأرض",
      "عرض اللوحة": "بطول واجهة المحل",
      "ارتفاع اللوحة": "يتم تحديدها وفقا للتصنيف الحضري.",
      "المسافة من نافذة العرض": "الحد الأدنى 10 سم.",
      "المسافة بين اللوحات": "الحد الأدنى 10 سم.",
      "بروز اللوحة": "الحد الأقصى 20 سم."
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel1.png'
  }, {
    id: 2,
    cardNumber: "02",
    title: "اللوحة العمودية",
    description:
      "هي لوحة تُثبّت بشكل عمودي على واجهة المبنى، مما يتيح رؤيتها بوضوح للمارة على أرصفة المشاة.",
    purpose: "",
    generalRules: [
      "يسمح بوضع اللوحة العمودية على المباني من الجهة اليمنى (فقط) للمتجر.",
      "يسمح بوضع اللوحة العمودية على المباني ذات الرواق على أن تكون داخل أو خارج الرواق من الجهة اليمنى للمتجر، وذلك حسب التشكيل المعماري للواجهة.",
      "لا يسمح باختلاف بروز اللوحات العمودية عن الواجهة على مستوى المبنى الواحد.",
      "بالمنطقة المركزية: غير مسموح بتركيب اللوحات العمودية خارج الرواق.",
      "بالمنطقة المركزية: يسمح بتركيب اللوحات العمودية للعلامات التجارية فقط على أن تكون داخل الرواق."
    ],
    regulations: [
      {
        name: "اللوحات في المباني ذات الأروقة بالمنطقة المركزية",
        regulation: {
          "1": {
            notAllowed: [
              { imgSrc: "/images/prep_panel_reg_1.png", description: "غير مسموح بتركيب اللوحات العمودية خارج الرواق بالمنطقة المركزية." }
            ],
            allowed: [
              { imgSrc: "/images/prep_panel_reg_2.png", description: "يسمح بتركيب اللوحات العمودية للعلامات التجارية فقط على أن تكون داخل الرواق." }
            ]
          }
        }
      }
    ],
    standardSpecs: {
      "التوجيه": "مسموح فقط بالتوجيه العمودي.",
      "المسافة إلى واجهة المبنى": "الحد الأقصى 20 سم.",
      "الارتفاع عن الأرض": "يتم تحديدها وفقا للتصنيف الحضري.",
      "الأبعاد": "60 × 60",
      "العمق": "لا تزيد عن 20 سم",
      "البروز": "80 سم"
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel3.png'
  }, {
    id: 3,
    cardNumber: "03",
    title: "اللوحة المظلة",
    description:
      "هي اللوحة المدمجة مع هيكل المظلة المثبتة على واجهة المحل.",
    purpose: "",
    generalRules: [
      "لا يُسمح بالتقدم بطلب لوحة المظلة إلا إذا كانت قائمة بترخيص نظامي ضمن النشاط التجاري.",
      "يلزم أن يكون النشاط التجاري يشغل كامل الدور الأرضي للمبنى.",
      "يلزم أن تكون اللوحة ظاهرة وواضحة (24) ساعة لتكون مرئية للجهات الرقابية."
    ],
    standardSpecs: {
      "الارتفاع عن الأرض": "يتم تحديدها وفقا للتصنيف الحضري.",
      "البروز": "يتم تحديدها وفقا للتصنيف الحضري.",
      "ارتفاع الكتابة": "لا يزيد عن 0.60 سم"
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel4.png'
  },
  {
    id: 4,
    cardNumber: "04",
    title: "لوحة نافذة العرض",
    description: "هي لوحة يتم تثبيتها في نافذة العرض للمتجر.",
    purpose: "",
    generalRules: [
      "يجب أن تكون لوحات النوافذ مصنوعة من فينيل أو لوحات مطبوعة (ستيكر) أو رسومات أو حفر على الزجاج وتكون مرئية من خارج المبنى.",
      "يجب عدم وضعها فوق مستوى الطابق الأرضي.",
      "يلزم أن تكون اللوحة ظاهرة وواضحة (24) ساعة لتكون مرئية للجهات الرقابية."
    ],
    regulations: [],
    standardSpecs: {
      "المساحة": "يتم تحديدها وفقا للتصنيف الحضري.",
      "العدد": "تخصص لوحة واحدة لكل نافذة عرض."
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel5.png'
  }, {
    id: 5,
    cardNumber: "05",
    title: "لوحة المستأجرين للأدوار العليا",
    description:
      "هي لوحة جدارية للتعريف بالمستخدمين في الأدوار العليا.",
    purpose: "",
    generalRules: [
      "يلزم أن تكون اللوحة بالقرب من المدخل على واجهة المبنى ضمن موقع محدد بمخطط واجهة المبنى المعتمد بالرخصة الإنشائية.",
      "في حالة عدم توفر مساحة متاحة لوضع لوحة المستأجرين للأدوار العليا فتوضع اللوحة بداخل المبنى."
    ],
    standardSpecs: {
      "ارتفاع اللوحة": "الحد الأقصى 1.5 م",
      "عرض اللوحة": "الحد الأقصى 1 م",
      "الارتفاع عن الأرض": "الحد الأدنى 50 سم.",
      "بعد اللوحة عن المدخل أو حافة المبنى أو أي من العناصر المعمارية": "الحد الأدنى 10 سم."
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel8.png'
  },
  {
    id: 6,
    cardNumber: "06",
    title: "لوحة العلامة التجارية",
    description:
      "هي لوحة مخصصة لعرض اسم المبنى أو النشاط الرئيسي، وعادة ما تكون في أعلى نقطة من المبنى، وتعد هذه اللوحة جزءًا من الهوية البصرية للمبنى وتستخدم لتسهيل التعرف على المبنى من مسافات بعيدة.",
    purpose: "",
    generalRules: [
      "يسمح بلوحة علامة تجارية واحدة لكل واجهة مطلة على شارع."
    ],
    standardSpecs: {
      "ارتفاع تثبيت اللوحة": "يتم تحديدها وفقا للتصنيف الحضري.",
      "ارتفاع اللوحة": "يتم تحديدها وفقا للتصنيف الحضري.",
      "عرض اللوحة": "يتم تحديدها وفقا للتصنيف الحضري."
    },
    colors: [],
    lighting: [],
    imageURL: '/images/panel7.png',
    regulations: []
  },
];

// Data for lighting section
export const lightingData: ProductAndLightCardProps[] = [
  {
    id: 1,
    title: 'الإضاءة من الخلف',
    image: "/images/lightImage1.png"
  },
  {
    id: 2,
    title: 'الإضاءة من الأمام',
    image: "/images/lightImage2.png"
  },
  {
    id: 3,
    title: 'الإضاءة من الجانب',
    image: "/images/lightImage3.png"
  },
  {
    id: 4,
    title: 'إضائة النيون',
    image: "/images/lightImage4.png"
  }
];


// Data for Products section
export const ProductsData: ProductAndLightCardProps[] = [
  {
    id: 1,
    title: 'لوحة مسطحة',
    image: "/images/productImg1.png"
  },
  {
    id: 2,
    title: 'حروف ثلاثية الابعاد',
    image: "/images/productImg2.png"
  },
  {
    id: 3,
    title: 'صندوق مضيء',
    image: "/images/productImg3.png"
  },
];

// data for urbanexample Images
type exampleImagesType = {
  src: string;
  alt: string;
  width: number;
  height: number;
}

export const exampleImages: exampleImagesType[] = [
  { src: "/images/example2.png", alt: "example2", width: 500, height: 300 },
  { src: "/images/example3.png", alt: "example2", width: 500, height: 300 },
  { src: "/images/mapImage3.png", alt: "example3", width: 500, height: 300 },
  { src: "/images/example4.png", alt: "example4", width: 500, height: 300 },
];

//Data for  permittedSigns section in UrbanClassification page
export const permittedSignsData = [
  { heading: 'لوحة الموازية لسطح الواجهة', title: 'الشروط الخاصة', description: '1/3 ارتفاع واجهة المحل بدون حد اقصى' },
  { heading: 'لوحة العلامة التجارية' },
  { heading: 'اللوحة المتعامدة على سطح الواجهة' },
  { heading: 'بوحة نافذة العرض', title: 'الشروط الخاصة', description: 'لا يتجاوز حجمها 50% من نافذة العرض وبحد أقصى 6م2' },
  { heading: 'لوحات المستأجرين لأدوار علوية', title: 'الشروط الخاصة', description: '1/3 ارتفاع واجهة المحل بدون حد اقصى' },
  { heading: 'لوحة المظلة' },
]

// Data for Products section in UrbanClassification page
export const ProductsDataUrban: ProductAndLightCardProps[] = [
  {
    id: 1,
    title: 'لوحة مسطحة',
    image: "/images/productImg7.png"
  },
  {
    id: 2,
    title: 'حروف ثلاثية الابعاد',
    image: "/images/productImg8.png"
  },
  {
    id: 3,
    title: 'صندوق مضيء',
    image: "/images/productImg6.png"
  }
];

// Data for lighting section in UrbanClassification page
export const lightingDataUrban: ProductAndLightCardProps[] = [{
  id: 1,
  title: 'الإضاءة من الخلف',
  image: "/images/lightImage1.png"
},
{
  id: 2,
  title: 'الإضاءة من الأمام',
  image: "/images/lightImage2.png"
},
{
  id: 3,
  title: 'الإضاءة من الجانب',
  image: "/images/lightImage3.png"
}];
