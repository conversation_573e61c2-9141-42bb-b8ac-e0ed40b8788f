"use client"

import PanelModal from "@/components/molecules/PanelModal";
import { MapPanelsCardProps } from "@/lib/types";
import Image from "next/image";
import { useState } from "react";


const MapPanelsCard = ({  title, className, isSelected, onToggleSelect }: MapPanelsCardProps) => {


  return (
    <div
      className={`text-xs md:text-sm lg:text-xl w-full max-w-72 max-h-16 mx-auto px-2 md:px-4 lg:px-6 py-3 md:py-5 lg:py-7 ${isSelected ? 'bg-[#0073670D]' : 'bg-white'} rounded-lg  outline-[1.60px] outline-offset-[-1.60px] ${isSelected ? 'outline-teal-700' : 'outline-zinc-500'} flex flex-col justify-center items-center gap-3.5 overflow-hidden hover:bg-[#0073670D] hover:outline-[1.60px] hover:outline-offset-[-1.60px] hover:outline-teal-700 cursor-pointer ${className}`}
      onClick={onToggleSelect}
    >
        {/* Title Text */}
        <div className="text-center text-black font-normal leading-7">
          {title}
        </div>
    </div>
  )
}

export default MapPanelsCard;