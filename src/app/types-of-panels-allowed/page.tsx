"use client"

import { useTranslations } from 'next-intl';
import { Button } from "@/components/atoms/Button";
import Image from "next/image";
import MapPanelsCard from './_components/MapPanelsCard';
import { BASE_PANELS } from '@/data/panels';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import MapInteractivePreview from '../commercial-panels-conditions-for-selected-area/_components/MapInteractivePreview';
import { useScreenSize } from '@/context/ScreenSizeContext';
import Header from '@/components/organisms/Header';


const TPAPage = () => {
    const t = useTranslations('MapPagePanelsDetails');
    const tCommon = useTranslations('Common');
    const router = useRouter();

    // read selected condition from sessionStorage (set on map page) to set the allowed panels
    const [condition, setCondition] = useState<string | null>(null);
    const [panels, setPanels] = useState([] as typeof BASE_PANELS);
    const [selectedClass, setSelectedClass] = useState<string | null>(null);
    const [selectedPanelTitle, setSelectedPanelTitle] = useState<string | null>(null);
    const [previewPanel, setPreviewPanel] = useState<typeof BASE_PANELS[0] | null>(null);
    const { isMobile } = useScreenSize();


    // console.log(`selectedPanelTitle is : ${selectedPanelTitle}`)

    useEffect(() => {
        try {
            const c = typeof window !== 'undefined' ? window.sessionStorage.getItem('selected_feature_attrs') : null;
            //console.log(`condition and SelectedFeatures are ${c}`)
            setCondition(c);
            if (!c || c.trim() === '') {
                setPanels(BASE_PANELS);
                setSelectedClass(null);
                return;
            }
            try {
                const cls = JSON.parse(c || 'null')?.[0]?.NewClass;
                setSelectedClass(typeof cls === 'string' ? (cls.trim() || null) : null);
            } catch {
                setSelectedClass(null);
            }
        } catch { }
    }, []);

    // Memoize the toggle function to prevent recreation on each render
    const handleToggleSelect = useCallback((card: typeof BASE_PANELS[0]) => {
        setSelectedPanelTitle(prev => {
            if (prev === card.title) {
                setPreviewPanel(null);
                return null;
            } else {
                setPreviewPanel(card);
                return card.title;
            }
        });
    }, []);

    // Memoize the panel cards to prevent unnecessary re-renders
    const panelCards = useMemo(() =>
        BASE_PANELS.map((card) => (
            <MapPanelsCard
                key={card.id}
                title={card.title}
                isSelected={selectedPanelTitle === card.title}
                onToggleSelect={() => handleToggleSelect(card)}
            />
        )), [selectedPanelTitle, handleToggleSelect]
    );

    console.log(`selectedClass is ${selectedClass}`)

    return (
        <main className="min-h-screen ">
            <Header />
            <div className="max-w-screen-lg mx-auto px-4">
                {/* Intro */}
                <h2 className="md:inline-block text-base md:text-xl lg:text-2xl font-bold text-black my-6 me-6">
                    {t("title")}
                </h2>
                <span className='inline-block p-2.5 text-sm lg:text-base font-bold text-primary bg-teal-700/10 rounded outline-1 outline-offset-[-1px] outline-teal-700'>
                    {selectedClass}
                </span>

                {/* the allowed panels in Mobile view */}
                {isMobile && (
                    <div className="mt-7 flex gap-1.5 max-[350px]:h-35">
                        {/* Allowed Panels */}
                        <div className="flex-[2] rounded-lg outline-[0.50px] outline-offset-[-0.50px] outline-neutral-400 px-1.5 py-2">
                            <div className="grid grid-cols-[1fr_auto_1fr] gap-0 items-baseline sm:items-center h-full">

                                <div className="ms-3 flex flex-col gap-5 sm:gap-6 items-start h-full justify-center  max-[350px]:justify-start ">
                                    {BASE_PANELS.slice(0, Math.ceil(BASE_PANELS.length / 2)).map((card) => (
                                        <p
                                            key={card.id}
                                            className={`cursor-pointer text-xs sm:text-base h-5 ${selectedPanelTitle === card.title ? 'font-bold text-primary' : ''}`}
                                            onClick={() => handleToggleSelect(card)}
                                        >
                                            {card.title}
                                        </p>
                                    ))}
                                </div>
                                {/* Separator Line */}
                                <div className=" w-[1px] h-full bg-[#999999]"></div>

                                <div className="ms-3 flex flex-col gap-5 sm:gap-6 items-start h-full justify-center max-[350px]:justify-start ">
                                    {BASE_PANELS.slice(Math.ceil(BASE_PANELS.length / 2)).map((card) => (
                                        <p
                                            key={card.id}
                                            className={`cursor-pointer text-xs sm:text-base h-5 ${selectedPanelTitle === card.title ? 'font-bold text-primary' : ''}`}
                                            onClick={() => handleToggleSelect(card)}
                                        >
                                            {card.title}
                                        </p>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Panel Preview */}
                        <div className="flex justify-center items-center flex-[1] rounded-lg outline-[0.50px] outline-offset-[-0.50px] outline-neutral-400 px-1.5 py-[10%]">
                            {previewPanel ? (
                                <div className="w-full max-w-sm">
                                    <Image
                                        src={previewPanel.imgSrc}
                                        alt={previewPanel.title}
                                        width={300}
                                        height={200}
                                        className="w-full object-contain h-25"
                                    />
                                </div>
                            ) : (
                                <div className='h-25 flex items-center justify-center'>
                                    <p className="text-center text-xs">لم يتم اختيار أي عنصر بعد، يرجى اختيار عنصر ليتم عرضه</p>
                                </div>
                            )}
                        </div>
                    </div>
                )}
                {/* navigation buttons */}
                <div className='flex justify-between items-center mb-6 md:mt-6 md:mb-9 max-sm:!mt-10'>
                    <a href={"/map"}>
                        <Button variant="outline3" className="!bg-primary !border-navHover">
                            <Image src="/images/arrowRight.svg" alt="arrow icon" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
                            <span>{tCommon("perviousStep")}</span>
                        </Button>
                    </a>
                    <Button
                        variant="outline3"
                        className="!bg-primary !border-navHover"
                        disabled={!selectedPanelTitle}
                        onClick={() => {
                            if (selectedPanelTitle) {

                                if (typeof window !== 'undefined') {
                                    window.sessionStorage.setItem('selected_panels', JSON.stringify([selectedPanelTitle]));
                                    if (selectedClass) {
                                        window.sessionStorage.setItem('selected_class', selectedClass);
                                    } else {
                                        window.sessionStorage.removeItem('selected_class');
                                    }
                                }
                                router.push('/commercial-panels-conditions-for-selected-area');
                            }
                        }}
                    >
                        <span>{tCommon("reportView")}</span>
                        <Image src="/images/arrow2.svg" alt="arrow icon" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
                    </Button>
                </div>
                {/*  The view of the mapScreen in Mobile view  */}
                {isMobile && <div className='rounded-lg outline-offset-[-1px] outline-zinc-800  h-[285px]  bg-primary p-1'>
                    <MapInteractivePreview className="!mb-0 !border-0 !rounded-lg flex-1" height="100%" />
                </div>}
                {/*  The view of the mapScreen in desktop  */}
                {!isMobile && <div className='grid grid-cols-2 gap-8 max-h-[700px] mb-5'>
                    <div className='grid grid-rows-2 max-h-[700px]'>
                        {/* allowed panels */}
                        <div className=' rounded-lg outline-1 outline-offset-[-1px] outline-zinc-800 p-8 mb-[18px]'>
                            {/* intro */}
                            <p className='text-base md:text-lg lg:text-xl font-medium leading-7 mb-8'>{t("intro")}</p>
                            {/* the allowed panels */}
                            <div className="grid grid-cols-2 gap-4 place-items-center">
                                {panelCards}
                            </div>
                        </div>

                        {/* panel preview */}
                        <div className='rounded-lg outline-1 outline-offset-[-1px] outline-zinc-800 p-8 overflow-hidden'>
                            {/* intro */}
                            <p className='text-base md:text-lg lg:text-xl font-medium leading-7 mb-8'>معاينة اللوحة</p>

                            {previewPanel ? (
                                <div className="flex flex-col items-center gap-4">
                                    <div className="w-full max-w-sm ">
                                        <Image
                                            src={previewPanel.imgSrc}
                                            alt={previewPanel.title}
                                            width={300}
                                            height={200}
                                            className="w-full h-auto object-contain rounded-lg border border-gray-200 max-h-[250px]"
                                        />
                                    </div>
                                </div>
                            ) : (
                                <p className='min-h-48 text-center'>لم يتم اختيار أي عنصر بعد، يرجى اختيار عنصر ليتم عرضه</p>
                            )}
                        </div>
                    </div>
                    {/* The mapScreen */}
                    <div className='flex-1 rounded-lg  pb-0 border-1 outline-offset-[-1px] outline-zinc-800 flex h-[700px] overflow-hidden'>
                        <MapInteractivePreview className="!mb-0 !border-0 !rounded-lg flex-1" height="100%" />
                    </div>
                </div>
                }
            </div>
        </main>
    )
}

export default TPAPage;
