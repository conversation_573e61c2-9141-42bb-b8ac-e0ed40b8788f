import PermittedSigns from '@/components/molecules/PermittedSignsCard'
import ProductAndLightCard from '@/components/molecules/Product&LightCard'
import { urbanServicesCommercial, urbanServicesZones } from '@/data/homePage'
import { exampleImages, lightingDataUrban, permittedSignsData, ProductsDataUrban } from '@/data/homePage'
import { useTranslations } from 'next-intl';
import { notFound } from 'next/navigation'
import { getTranslations } from 'next-intl/server';
import { deslugify, slugify } from '@/utils/slugify';
import Image from 'next/image';

// Generate static params for both data sources with slugified titles
export async function generateStaticParams() {
    const commercialParams = urbanServicesCommercial.map((data) => ({
        slug: `commercial-${slugify(data.title)}`,
    }));

    const zonesParams = urbanServicesZones.map((data) => ({
        slug: `zones-${slugify(data.title)}`,
    }));

    // Combine both arrays
    return [...commercialParams, ...zonesParams];
}

const DetailsPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
    const { slug } = await params;
    const tCommercial = await getTranslations('urbanServicesCommercial');
    const tZones = await getTranslations('urbanServicesZones');
    const t = await getTranslations("urbanClassification");
    const tUrban = await getTranslations("UrbanClassificationPage");

    let data;
    let dataType = '';
    let dataTitle = '';

    // Parse the slug to determine type and ID
    if (slug.startsWith('commercial-')) {
        dataType = 'commercial';
        dataTitle = deslugify(slug.replace('commercial-', ''));
        data = urbanServicesCommercial.find((item) => item.title === dataTitle);
    } else if (slug.startsWith('zones-')) {
        dataType = 'zones';
        dataTitle = deslugify(slug.replace('zones-', ''));
        data = urbanServicesZones.find((item) => item.title === dataTitle);
    } else {
        // Invalid slug format
        notFound();
    }

    // If data not found in the specified source, return 404
    if (!data) {
        notFound();
    }

    // Extract data from the found item
    const displayPermittedSignsData = data.permittedSignsData || permittedSignsData;
    const displayExampleImages = data.exampleImages || exampleImages;
    const displayProductsData = data.productsData || ProductsDataUrban;
    const displayLightingDataUrban = data.lightingDataUrban || lightingDataUrban;

    return (
        <section className='mx-auto flex flex-col my-10'>
            {/* main heading */}
            <h3 className='text-xl md:text-2xl lg:text-3xl text-center font-bold'>
                {dataType === 'commercial'
                    ? tCommercial(`${data.id}.title`)
                    : tZones(`${data.id}.title`)
                }
            </h3>

            {/* intro */}
            <div className='my-6 px-4 sm:px-6 lg:px-8'>
                <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
                    {data.intro?.title || tUrban("urbanSupTitle1")}
                </h4>
                <p>{data.intro?.description || tUrban("urbanIntro")}</p>
            </div>

            {/* Permitted Signs */}
            {displayPermittedSignsData.length > 0 && (
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
                        {tUrban("urbanSupTitle2")}
                    </h4>
                    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5'>
                        {displayPermittedSignsData.map((item, index) => (
                            <PermittedSigns
                                key={item.heading || index}
                                heading={item.heading}
                                title={item.title}
                                description={item.description}
                            />
                        ))}
                    </div>
                </div>
            )}

            {/* Examples */}
            {displayExampleImages.length > 0 && (
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
                        {tUrban("examples")}
                    </h4>
                    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5'>
                        {displayExampleImages.map((image, index) => (
                            <Image
                                key={index}
                                src={image.src}
                                alt={image.alt}
                                width={image.width}
                                height={image.height}
                                className="w-full h-full rounded-lg"
                            />
                        ))}
                    </div>
                </div>
            )}

            {/* Products */}
            {displayProductsData.length > 0 && (
                <div className='bg-[#E1F2DB]'>
                    <div className='px-4 sm:px-6 lg:px-8 py-5 text-black' style={{
                        backgroundImage: `url(/images/LightingBackground.svg)`,
                        backgroundSize: '25% auto',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'top left',
                    }}>
                        <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-10'>
                            {tUrban("productTypes")}
                        </h4>
                        {/* Main Content */}
                        <div className="mx-auto container flex flex-col">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                                {displayProductsData.map((card) => (
                                    <ProductAndLightCard
                                        id={card.id}
                                        key={card.id}
                                        image={card.image}
                                        title={card.title}
                                        imageHeight="xl"
                                        className='!bg-transparent !border-none shadow-none !outline-0'
                                        isLightCard
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Lighting */}
            {displayLightingDataUrban.length > 0 && (
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
                        {tUrban("lightingTypes")}
                    </h4>
                    {/* Main Content */}
                    <div className="mx-auto flex flex-col">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                            {displayLightingDataUrban.map((card) => (
                                <ProductAndLightCard
                                    id={card.id}
                                    key={card.id}
                                    image={card.image}
                                    title={card.title}
                                    imageHeight="lg"
                                    isLightCard
                                />
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </section>
    );
};

export default DetailsPage;
