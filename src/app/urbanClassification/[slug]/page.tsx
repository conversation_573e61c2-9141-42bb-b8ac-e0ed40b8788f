import PermittedSigns from '@/components/molecules/PermittedSignsCard'
import { urbanServicesCommercial, urbanServicesZones } from '@/data/homePage'
import { exampleImages, lightingDataUrban, permittedSignsData, ProductsDataUrban } from '@/data/homePage'
import { notFound } from 'next/navigation'
import { getTranslations } from 'next-intl/server';
import { deslugify, slugify } from '@/utils/slugify';
import Image from 'next/image';
import ProductsAndLightingSection from '../_components/ProductsAndLightingSection';
import Header from '@/components/organisms/Header';


// Generate static params for both data sources with slugified titles
export async function generateStaticParams() {
  const commercialParams = urbanServicesCommercial.map((data) => ({
    slug: `commercial-${slugify(data.title)}`,
  }));

  const zonesParams = urbanServicesZones.map((data) => ({
    slug: `zones-${slugify(data.title)}`,
  }));

  // Combine both arrays
  return [...commercialParams, ...zonesParams];
}

const DetailsPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  const tCommercial = await getTranslations('urbanServicesCommercial');
  const tZones = await getTranslations('urbanServicesZones');
  const tUrban = await getTranslations("UrbanClassificationPage");

  let data;
  let dataType = '';
  let dataTitle = '';

  // Parse the slug to determine type and ID
  if (slug.startsWith('commercial-')) {
    dataType = 'commercial';
    dataTitle = deslugify(slug.replace('commercial-', ''));
    data = urbanServicesCommercial.find((item) => item.title === dataTitle);
  } else if (slug.startsWith('zones-')) {
    dataType = 'zones';
    dataTitle = deslugify(slug.replace('zones-', ''));
    data = urbanServicesZones.find((item) => item.title === dataTitle);
  } else {
    // Invalid slug format
    notFound();
  }

  // If data not found in the specified source, return 404
  if (!data) {
    notFound();
  }

  // Extract data from the found item
  const displayPermittedSignsData = data.permittedSignsData || permittedSignsData;
  const displayExampleImages = data.exampleImages || exampleImages;
  const displayProductsData = data.productsData || ProductsDataUrban;
  const displayLightingDataUrban = data.lightingDataUrban || lightingDataUrban;

  return (
    <section className='mx-auto flex flex-col'>
      <Header />
      {/* main heading */}
      <h3 className='text-xl md:text-2xl lg:text-3xl text-center font-bold'>
        {dataType === 'commercial'
          ? tCommercial(`${data.id}.title`)
          : tZones(`${data.id}.title`)
        }
      </h3>

      {/* intro */}
      <div className='my-6 px-4 sm:px-6 lg:px-8'>
        <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
          {data.intro?.title || tUrban("urbanSupTitle1")}
        </h4>
        <p>{data.intro?.description || tUrban("urbanIntro")}</p>
      </div>

      {/* Permitted Signs */}
      {displayPermittedSignsData.length > 0 && (
        <div className='my-6 px-4 sm:px-6 lg:px-8'>
          <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
            {tUrban("urbanSupTitle2")}
          </h4>
          <div className='grid grid-cols-2 lg:grid-cols-4 gap-5 items-stretch'>
            {displayPermittedSignsData.map((item, index) => (
              <PermittedSigns
                key={item.heading || index}
                heading={item.heading}
                title={item.title}
                description={item.description}
              />
            ))}
          </div>
        </div>
      )}

      {/* Examples */}
      {displayExampleImages.length > 0 && (
        <div className='my-6 px-4 sm:px-6 lg:px-8'>
          <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>
            {tUrban("examples")}
          </h4>
          <div className='grid grid-cols-2  gap-3 md:gap-5'>
            {displayExampleImages.map((image, index) => (
              <Image
                key={index}
                src={image.src}
                alt={image.alt}
                width={image.width}
                height={image.height}
                className="w-full h-full rounded-lg"
              />
            ))}
          </div>
        </div>
      )}

      {/* Products and Lighting */}
      <ProductsAndLightingSection
        displayProductsData={displayProductsData}
        displayLightingDataUrban={displayLightingDataUrban}
      />
    </section>
  );
};

export default DetailsPage;
