import PermittedSigns from '@/components/molecules/PermittedSignsCard'
import ProductAndLightCard from '@/components/molecules/Product&LightCard'
import Header from '@/components/organisms/Header'
import { exampleImages, lightingDataUrban, permittedSignsData, ProductsDataUrban } from '@/data/homePage'
import { useTranslations } from 'next-intl'
import React from 'react'
import Image from 'next/image'


const UrbanClassificationPage = () => {
    const t = useTranslations("urbanClassification")
    const tUrban = useTranslations("UrbanClassificationPage")

    return (
        <>
            <Header />
            <section className='mx-auto flex flex-col'>
                {/* main heading */}
                <h3 className='text-xl md:text-2xl lg:text-3xl text-center font-bold'>
                    {t("urbanTitle")}
                </h3>
                {/* intro */}
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'> {tUrban("urbanSupTitle1")}  </h4>
                    <p>{tUrban("urbanIntro")}</p>
                </div>
                {/* Permitted Signs */}
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>{tUrban("urbanSupTitle2")}</h4>
                    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5'>
                        {permittedSignsData.map((item) => (
                            <PermittedSigns key={item.heading} heading={item.heading} title={item.title} description={item.description} />
                        ))}
                    </div>
                </div>
                {/* Examples */}
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>{tUrban("examples")}</h4>
                    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5'>
                        {exampleImages.map((image, index) => (
                            <Image
                                key={index}
                                src={image.src}
                                alt={image.alt}
                                width={image.width}
                                height={image.height}
                                className="w-full h-auto rounded-lg"
                            />
                        ))}
                    </div>
                </div>
                {/* products */}
                <div className='bg-[#E1F2DB]'>
                    <div className=' px-4 sm:px-6 lg:px-8 py-5 text-black' style={{
                        backgroundImage: `url(/images/LightingBackground.svg)`,
                        backgroundSize: '25% auto',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'top left',
                    }}>
                        <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-10'>{tUrban("productTypes")}</h4>
                        {/* Main Content */}
                        <div className="mx-auto container flex flex-col">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                                {ProductsDataUrban.map((card) => (
                                    <ProductAndLightCard
                                        id={card.id}
                                        key={card.id}
                                        image={card.image}
                                        title={card.title}
                                        imageHeight="xl"
                                        className='!bg-transparent !border-none shadow-none !outline-0 '
                                        isLightCard
                                    />
                                ))}
                            </div>
                        </div>
                        {/* <Products /> */}
                    </div>
                </div>
                {/* Lighting */}
                <div className='my-6 px-4 sm:px-6 lg:px-8'>
                    <h4 className='text-sm md:text-md lg:text-xl font-bold text-primary pb-5'>{tUrban("lightingTypes")} </h4>
                    {/* Main Content */}
                    <div className="mx-auto flex flex-col">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                            {lightingDataUrban.map((card) => (
                                <ProductAndLightCard
                                    id={card.id}
                                    key={card.id}
                                    image={card.image}
                                    title={card.title}
                                    imageHeight="lg"
                                    isLightCard
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}

export default UrbanClassificationPage