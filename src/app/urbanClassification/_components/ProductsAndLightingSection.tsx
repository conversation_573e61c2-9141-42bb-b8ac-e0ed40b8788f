'use client';

import ProductAndLightCard from '@/components/molecules/Product&LightCard';
import ResponsiveCarouselGrid from '@/components/molecules/ProductCarousel';
import { useScreenSize } from '@/context/ScreenSizeContext';
import { useTranslations } from 'next-intl';

const ProductsAndLightingSection = ({
    displayProductsData,
    displayLightingDataUrban,
}: {
    displayProductsData: any[];
    displayLightingDataUrban: any[];
}) => {
    const { isMobile } = useScreenSize();
    const tUrban = useTranslations("UrbanClassificationPage");
    return (
        <>
            {/* Products */}
            {displayProductsData.length > 0 && (
                <div className="bg-white md:bg-[#E1F2DB]">
                    <div
                        className="px-4 sm:px-6 lg:px-8 py-5 text-black"
                        style={{
                            backgroundImage: isMobile ? '' : 'url(/images/LightingBackground.svg)',
                            backgroundSize: '25% auto',
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'top left',
                        }}
                    >
                        <h4 className="text-sm md:text-md lg:text-xl font-bold text-primary pb-10">
                            {tUrban('productTypes')}
                        </h4>
                        {/* Main Content */}
                        <ResponsiveCarouselGrid
                            data={displayProductsData}
                            gridCols={`md:grid-cols-2 lg:grid-cols-3 grid-cols-1`}
                            renderCard={(card) => (
                                <ProductAndLightCard
                                    key={card.id}
                                    id={card.id}
                                    image={card.image}
                                    title={card.title}
                                    imageHeight="sm"
                                />
                            )}
                        />
                    </div>
                </div>
            )}

            {/* Lighting */}
            {displayLightingDataUrban.length > 0 && (
                <div className="my-6 px-4 sm:px-6 lg:px-8">
                    <h4 className="text-sm md:text-md lg:text-xl font-bold text-primary pb-5">
                        {tUrban('lightingTypes')}
                    </h4>
                    {/* Main Content */}
                    <div className="mx-auto flex flex-col">
                        <div className="grid grid-cols-2 lg:grid-cols-3 gap-10">
                            {displayLightingDataUrban.map((card) => (
                                <ProductAndLightCard
                                    id={card.id}
                                    key={card.id}
                                    image={card.image}
                                    title={card.title}
                                    imageHeight={isMobile ? 'sm' : 'lg'}
                                    isLightCard
                                />
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ProductsAndLightingSection;