@import url('https://fonts.googleapis.com/css2?family=El+Messiri:wght@400..700&display=swap');
@import "tailwindcss";

@theme {
  --color-primary: #066058;
  --color-secondary: #E1F2DB;
  --color-text1: #6CBE4A · 73%;
  --color-border-primary:#007367;
  --color-border-accent:#0F0F0F1A;
  --color-borderSecondary:#D9E5DD;
  --color-black: #0F0F0F;
  --color-gray: #747474;
  --color-gray-light: #E1E1E1;
  --color-background: #FAFAFA;
  --color-secBackground:#01433C;
  --color-thrBackground:#F1F7F3;
  --color-forthBackground:#006CF233;
  --color-formBackground: #A4C7C3;
  --color-formText:#01413A;
  --color-inputBackground:#CDDEDC;
  --color-textError:oklch(57.7% 0.245 27.325);
  --color-navHover:#01413A;
  --color-slider-background:#F3F7FB;


  --gradient-primary-start: #81C18A;
  --gradient-primary-end: #027468;
  --gradient-secondary-start: #01413A;
  --gradient-secondary-end: #03A795;
  --gradient-accent-start: #6CBE4A;
  --gradient-accent-end: #E1F2DB;
}


* {
  box-sizing: border-box;
}

html, body {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

body {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 36px;
  margin: 0;
  padding: 0;
}

body > * {
  max-width: 100%;
  width: 100%;
}

.container {
  width: 80%;
  max-width: 1440px;
}

.el-messiri{
  font-family: "El Messiri", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* RTL-specific spacing adjustments */
[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Break Point for the navMenu   */

@media (max-width: 890px) {
  .hide-at-800 {
    display: none !important;
  }
}

@media (max-width: 890px) {
  .show-at-800 {
    display: block !important;
  }
}

