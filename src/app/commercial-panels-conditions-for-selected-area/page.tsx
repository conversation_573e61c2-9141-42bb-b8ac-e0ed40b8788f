"use client"
import { Button } from "@/components/atoms/Button";
import React, { useCallback, useEffect, useRef, useState } from 'react'
import Image from "next/image";
import { useTranslations } from 'next-intl';
import { Feature, SinglePanelsData, TableColumn, TableRow } from "@/lib/types";
import Table from "./_components/Table";
import MapInteractivePreview, { MapInteractivePreviewHandle } from "./_components/MapInteractivePreview";
import { exampleImages, lightingDataUrban, permittedSignsData, ProductsDataUrban } from "@/data/homePage";
import PermittedSigns from "@/components/molecules/PermittedSignsCard";
import ProductAndLightCard from "@/components/molecules/Product&LightCard";
import jsPDF from 'jspdf';
import { addSectionToPdf } from "@/utils/addSectionToPdf";
import { PanelsData } from "@/data/homePage";
import SelectedPanelDetails from "./_components/SelectedPanelDetails";



const CPCPage = () => {
  const t = useTranslations("urbanClassification")
  const tCommon = useTranslations('Common');
  const tUrban = useTranslations("UrbanClassificationPage")
  const [panels, setPanels] = useState<string[]>([])
  const [selectedFeatureObj, setSelectedFeatureObj] = useState<Feature | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [pdfMode, setPdfMode] = useState(false)
  const [isMapLoading, setIsMapLoading] = useState(true)
  const [activePanel, setActivePanel] = useState<SinglePanelsData | null>(null);
  const section1Ref = useRef<HTMLDivElement | null>(null)
  const section2Ref = useRef<HTMLDivElement | null>(null)
  const section3Ref = useRef<HTMLDivElement | null>(null)
  const section4Ref = useRef<HTMLDivElement | null>(null)
  const section5Ref = useRef<HTMLDivElement | null>(null)
  const mapRef = useRef<MapInteractivePreviewHandle | null>(null)
  const permittedGridRef = useRef<HTMLDivElement | null>(null)
  const titleOfUrbanClass = selectedFeatureObj?.NewClass != null ? String(selectedFeatureObj.NewClass) : '-';
  const [municipalityMapping, setMunicipalityMapping] = useState<Record<string, string>>({});
  const [coordinates, setCoordinates] = useState<any>(null);

  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const p = window.sessionStorage.getItem('selected_panels')
        const features = window.sessionStorage.getItem('selected_feature_attrs');
        const mapping = window.sessionStorage.getItem('municipality_domain_mapping');
        const coords = window.sessionStorage.getItem('selected_coordinates');

        setPanels(p ? JSON.parse(p) : [])
        setMunicipalityMapping(mapping ? JSON.parse(mapping) : {});
        setCoordinates(coords ? JSON.parse(coords) : null);
        // console.log(`coordinates is ${JSON.stringify(coords)}`)
        // Search in PanelsData with the panels title and get the panel item
        if (p) {
          const selectedPanelTitles = JSON.parse(p);
          //console.log(`selectedPanelTitles is ${selectedPanelTitles}`)
          if (selectedPanelTitles.length > 0) {
            const foundPanel = PanelsData.find(panel =>
              selectedPanelTitles.includes(panel.title)
            ) || null;
            //console.log(`foundPanel is ${foundPanel}`)
            setActivePanel(foundPanel as SinglePanelsData);
          }
        }
        // parse first feature object for table binding
        try {
          const arr = features ? JSON.parse(features) : []
          //console.log(`arr is ${JSON.stringify(arr)}`)
          setSelectedFeatureObj(Array.isArray(arr) && arr.length > 0 ? (arr[0] as Feature) : null)
          //console.log(`selectedFeatureObj is ${JSON.stringify(Array.isArray(arr) && arr.length > 0 ? (arr[0] as Feature) : null)}`)
        } catch {
          setSelectedFeatureObj(null)
        }
      }
    } catch {
      setPanels([])
    }
  }, [])

  // Equalize heights for permitted signs cards
  const equalizePermittedHeights = () => {
    const root = permittedGridRef.current;
    if (!root) return;
    const cards = Array.from(root.querySelectorAll<HTMLElement>('.perm-sign-card'));
    if (!cards.length) return;
    // reset
    cards.forEach(c => (c.style.minHeight = 'auto'));
    const wrappers = Array.from(root.querySelectorAll<HTMLElement>('.perm-sign-card .pdf-border'));
    wrappers.forEach(w => (w.style.minHeight = 'auto'));

    // measure tallest among wrappers (when in pdf mode) or cards
    const measureTargets = wrappers.length ? wrappers : cards;
    const maxH = measureTargets.reduce((m, el) => Math.max(m, el.offsetHeight), 0);
    // apply
    cards.forEach(c => (c.style.minHeight = maxH + 'px'));
    if (wrappers.length) wrappers.forEach(w => (w.style.minHeight = maxH + 'px'));
  };

  useEffect(() => {
    // run after initial render
    const id = setTimeout(equalizePermittedHeights, 50);
    const onResize = () => equalizePermittedHeights();
    window.addEventListener('resize', onResize);
    return () => {
      clearTimeout(id);
      window.removeEventListener('resize', onResize);
    };
  }, []);

  const columns: TableColumn[] = [
    { key: 'element', label: 'العنصر' },
    { key: 'details', label: 'التفاصيل' }
  ];

  const sampleData: TableRow[] = [
    { element: 'البلدية', details: selectedFeatureObj?.Municapility != null ? (municipalityMapping[String(selectedFeatureObj.Municapility)] || String(selectedFeatureObj.Municapility)) : '-' },
    { element: 'الحي', details: selectedFeatureObj?.PLAN_NAME != null ? String(selectedFeatureObj.PLAN_NAME) : '-' },
    { element: 'رقم المخطط', details: selectedFeatureObj?.PLAN_NO != null ? String(selectedFeatureObj.PLAN_NO) : '-' },
    { element: 'رقم القطعة', details: selectedFeatureObj?.PARCELNO != null ? String(selectedFeatureObj.PARCELNO) : '-' },
    { element: 'نوع الاشتراط', details: selectedFeatureObj?.NewClass != null ? String(selectedFeatureObj.NewClass) : '-' },
    { element: 'نوع اللوحة', details: panels && panels.length > 0 ? panels.join(', ') : '-' },
    { element: 'الإحداثيات', details: coordinates ? `${coordinates.center[1].toFixed(6)}, ${coordinates.center[0].toFixed(6)}` : '-' }
  ];

  const handleGeneratePdf = async () => {
    if (isGenerating) return;
    setIsGenerating(true);
    try {
      setPdfMode(true);
      // allow UI to re-render/hide buttons and add PDF-only borders
      await new Promise((r) => setTimeout(r, 500));
      // equalize heights again with PDF-only styles
      equalizePermittedHeights();

      // Ensure map canvas is printable by overlaying a screenshot
      await mapRef.current?.prepareForPrint();

      const html2canvas = (await import('html2canvas')).default;
      const pdf = new jsPDF('p', 'mm', 'a4');

      // Set line width for border (used when drawing on top)
      pdf.setLineWidth(0.2);
      pdf.setDrawColor(150);

      // Section 1: top content (w/o buttons due to pdfMode)
      if (section1Ref.current) {
        const el1 = section1Ref.current;
        const canvas1 = await html2canvas(el1, {
          scale: window.devicePixelRatio,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false,
          windowWidth: el1.scrollWidth,
          windowHeight: el1.scrollHeight,
          ignoreElements: (el) => !!(el as HTMLElement).hasAttribute && (el as HTMLElement).hasAttribute('data-ignore-pdf')
        });
        addSectionToPdf(pdf, canvas1, { marginXmm: 10 });
      }


      // Section 2: urban classification and examples
      if (section2Ref.current) {
        pdf.addPage();
        const el2 = section2Ref.current;
        const canvas2 = await html2canvas(el2, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false,
          windowWidth: el2.scrollWidth,
          windowHeight: el2.scrollHeight,
          ignoreElements: (el) => !!(el as HTMLElement).hasAttribute && (el as HTMLElement).hasAttribute('data-ignore-pdf')
        });
        addSectionToPdf(pdf, canvas2);
      }

      // Section 3: Products section
      if (section3Ref.current) {
        pdf.addPage();
        const el3 = section3Ref.current;
        const canvas3 = await html2canvas(el3, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false,
          windowWidth: el3.scrollWidth,
          windowHeight: el3.scrollHeight,
          ignoreElements: (el) => !!(el as HTMLElement).hasAttribute && (el as HTMLElement).hasAttribute('data-ignore-pdf')
        });
        addSectionToPdf(pdf, canvas3);
      }


      // start a new page for the lighting section
      if (section4Ref.current) {
        pdf.addPage();
        const el4 = section4Ref.current;
        const canvas4 = await html2canvas(el4, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false,
          windowWidth: el4.scrollWidth,
          windowHeight: el4.scrollHeight,
          ignoreElements: (el) => !!(el as HTMLElement).hasAttribute && (el as HTMLElement).hasAttribute('data-ignore-pdf')
        });
        addSectionToPdf(pdf, canvas4);
      }
      // Section 5: Panles section
      if (section5Ref.current) {
        pdf.addPage();
        const el5 = section5Ref.current;
        const canvas5 = await html2canvas(el5, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false,
          windowWidth: el5.scrollWidth,
          windowHeight: el5.scrollHeight,
          ignoreElements: (el) => !!(el as HTMLElement).hasAttribute && (el as HTMLElement).hasAttribute('data-ignore-pdf')
        });
        addSectionToPdf(pdf, canvas5);
      }

      pdf.save('commercial-panels-conditions.pdf');
    } catch (e) {

      console.error('Failed to generate PDF', e);
    } finally {
      mapRef.current?.cleanupAfterPrint();
      setPdfMode(false);
      setIsGenerating(false);
    }
  };

  const handleIsMapLoading = useCallback((isLoading: boolean) => {
    setIsMapLoading(isLoading)
  }, [])

  return (
    <section className={`mx-auto flex flex-col ${!pdfMode ? 'container' : ''}`}>
      {isGenerating && (
        <div className="fixed inset-0 z-[1000] bg-black/40 flex items-center justify-center" data-ignore-pdf>
          <div className="bg-white rounded-2xl p-6 shadow-xl flex items-center gap-4">
            <div className="w-6 h-6 rounded-full border-2 border-primary border-t-transparent animate-spin" />
            <div className="text-base">جاري إنشاء التقرير…</div>
          </div>
        </div>
      )}
      {/* main heading */}
      <div className='flex justify-between items-center mt-16 mb-9'>
        {!pdfMode && (
          <>
            <a href={"/types-of-panels-allowed"}>
              <Button variant="outline3" className="!bg-primary !border-navHover">
                <Image src="/images/arrowRight.svg" alt="arrow icon" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
                <span>{tCommon("perviousStep")}</span>
              </Button>
            </a>
            <p className={`text-sm ${!pdfMode ? 'md:text-lg lg:text-2xl' : ''}`}>
              اشتراطات اللوحات التجارية لقطعة الأرض المختارة
            </p>
            <Button
              variant="outline3"
              className={`!bg-primary !border-navHover ${isMapLoading ? "pointer-events-none opacity-50" : ""}`}
              onClick={handleGeneratePdf}
              disabled={isGenerating}
            >
              <span>{isGenerating ? tCommon("loading") : tCommon("reportPrint")}</span>
              <Image src="/images/arrow2.svg" alt="arrow icon" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
            </Button>
          </>
        )}
      </div>

      {/* Section 1: title + map + table (buttons hidden during PDF) */}
      <div ref={section1Ref} className={`${pdfMode ? "w-[1280px] " : " "}`}>
        {pdfMode && (
          <p className={`text-2xl mb-6 text-center`}>
            اشتراطات اللوحات التجارية لقطعة الأرض المختارة
          </p>
        )}
        {/* MapScreen (Interactive filtered preview) */}

        <MapInteractivePreview ref={mapRef} setIsLoading={handleIsMapLoading} className="!h-150" height="100%" />

        {/* the table of data */}
        <Table
          columns={columns}
          data={sampleData}
          className=" mb-20"
        />
      </div>

      {/* Section 2: urban classification and below (until lighting) */}
      <div ref={section2Ref} className={`${pdfMode ? "w-[1280px] " : " "}`}>
        {/* urban classification main content */}
        <h3 className={`text-xl ${!pdfMode ? 'md:text-2xl lg:text-3xl' : ''} text-center font-bold`}>
          {t("urbanTitle2")}
        </h3>
        {/* intro */}
        <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-5`}> {titleOfUrbanClass}  </h4>
          <p>{tUrban("urbanIntro")}</p>
        </div>
        {/* Permitted Signs */}
        <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`} ref={permittedGridRef}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-5`}>{tUrban("urbanSupTitle2")}</h4>
          <div className={`grid  ${!pdfMode ? 'md:grid-cols-2 lg:grid-cols-4 grid-cols-1' : 'grid-cols-4'} gap-5 items-stretch`}>
            {permittedSignsData.map((item) => (
              <PermittedSigns
                key={item.heading}
                heading={item.heading}
                title={item.title}
                description={item.description}
                pdfHighlight={pdfMode}
              />
            ))}
          </div>
        </div>
        {/* Examples */}
        <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-5`}>{tUrban("examples")}</h4>
          <div className={`grid ${!pdfMode ? 'md:grid-cols-2 lg:grid-cols-4 grid-cols-1' : 'grid-cols-4'} gap-5`}>
            {exampleImages.map((image, index) => (
              <Image
                key={index}
                src={image.src}
                alt={image.alt}
                width={image.width}
                height={image.height}
                className="w-full h-full rounded-lg"
              />
            ))}
          </div>
        </div>
      </div>
      {/* products */}
      <div ref={section3Ref} className={`bg-[#E1F2DB] ${pdfMode ? "w-[1280px] " : " "}`}>
        <div className={` px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''} py-5 text-black`} style={{
          backgroundImage: `url(/images/LightingBackground.svg)`,
          backgroundSize: '25% auto',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'top left',
        }}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-10`}>{tUrban("productTypes")}</h4>
          {/* Main Content */}
          <div className={`mx-auto container flex flex-col`}>
            <div className={`grid ${!pdfMode ? 'md:grid-cols-2 lg:grid-cols-3 grid-cols-1' : 'grid-cols-3'} gap-10`}>
              {ProductsDataUrban.map((card) => (
                <ProductAndLightCard
                  id={card.id}
                  key={card.id}
                  image={card.image}
                  title={card.title}
                  imageHeight="xl"
                  className='!bg-transparent !border-none shadow-none !outline-0 '
                  isLightCard
                />
              ))}
            </div>
          </div>
          {/* <Products /> */}
        </div>
      </div>

      {/* Section 3: Lighting types on separate page */}
      <div ref={section4Ref} className={`${pdfMode ? "w-[1280px] " : " "}`}>
        <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-5`}>{tUrban("lightingTypes")} </h4>
          {/* Main Content */}
          <div className="mx-auto flex flex-col">
            <div className={`grid ${!pdfMode ? 'md:grid-cols-2 lg:grid-cols-3 grid-cols-1' : 'grid-cols-3'} gap-10`}>
              {lightingDataUrban.map((card) => (
                <ProductAndLightCard
                  id={card.id}
                  key={card.id}
                  image={card.image}
                  title={card.title}
                  imageHeight="lg"
                  isLightCard
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Selected Panel Details */}
      <div ref={section5Ref} className={`${pdfMode ? "w-[1280px]" : " "}`}>
        <SelectedPanelDetails activePanel={activePanel} panels={panels} pdfMode={pdfMode} />
      </div>

      {/* <pre>{JSON.stringify({ selectedFeatureObj }, null, 2)}</pre> */}
    </section>
  )
}

export default CPCPage
