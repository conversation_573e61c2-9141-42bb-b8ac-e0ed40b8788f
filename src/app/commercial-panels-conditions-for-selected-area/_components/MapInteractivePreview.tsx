"use client"
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import Script from "next/script";
import Image from 'next/image';

type MapInteractivePreviewProps = {
  className?: string
  height?: string
  setIsLoading?: (isLoading: boolean) => void
}

export type MapInteractivePreviewHandle = {
  prepareForPrint: () => Promise<void>;
  cleanupAfterPrint: () => void;
}


const MapInteractivePreview = forwardRef<MapInteractivePreviewHandle, MapInteractivePreviewProps>(({ className, setIsLoading, height }: MapInteractivePreviewProps, ref) => {
  const viewMapRef = useRef<any>(null);
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  useEffect(() => {
    const mapState = typeof window !== 'undefined'
      ? window.sessionStorage.getItem('cpc_map_state')
      : null;

    if (!mapState) return;

    try {
      const state = JSON.parse(mapState);

      (window as any).require([
        "esri/Map",
        "esri/views/MapView",
        "esri/layers/MapImageLayer",
        "esri/geometry/Extent"
      ], (Map: any, MapView: any, MapImageLayer: any, Extent: any) => {
        const map = new Map({
          basemap: state.basemap === 'satellite' ? 'satellite' : 'topo-vector'
        });

        const layer = new MapImageLayer({
          url: "https://namaa-gis.kharetatalenmaa.sa/server/rest/services/MadinaPart_MIL1/MapServer",
        });
        map.add(layer);

        const viewMap = new MapView({
          container: "cpcViewDiv",
          map: map,
          center: state.center,
          zoom: state.zoom,
        });

        const container = document.getElementById("cpcViewDiv");
        if (container) {
          container.style.borderRadius = "20px";
          container.style.overflow = "hidden";
          // container.style.pointerEvents = "none";
        }

        viewMapRef.current = viewMap;

        // Apply filters and extent when map loads
        viewMap.when(() => {
          layer.when(() => {
            // Apply definition expression
            const sublayer = layer.sublayers.find((sl: any) => sl?.id === state.targetLayerId);
            if (sublayer && state.definitionExpression) {
              sublayer.definitionExpression = state.definitionExpression;
            }

            // Set extent
            const extent = new Extent({
              xmin: state.extent.xmin,
              ymin: state.extent.ymin,
              xmax: state.extent.xmax,
              ymax: state.extent.ymax,
              spatialReference: state.extent.spatialReference
            });

            viewMap.goTo(extent).catch(() => { });
            if(setIsLoading){
              setTimeout(() => {
                setIsLoading(false)
              }, 1000)
            }

          });
        });
      });
    } catch (error) {
      console.error('Failed to initialize CPC map:', error);
    }
  }, [setIsLoading]);

  useImperativeHandle(ref, () => ({
    prepareForPrint: async () => {
      try {
        const view = viewMapRef.current;
        if (!view || typeof view.takeScreenshot !== 'function') return;
        await view.when();
        const shot = await view.takeScreenshot({ format: 'png', quality: 1 });
        if (shot?.dataUrl) setScreenshotUrl(shot.dataUrl);
        // allow the overlay img to render before capture
        await new Promise((r) => setTimeout(r, 50));
      } catch {
        // ignore screenshot failures; keep map as-is
      }
    },
    cleanupAfterPrint: () => {
      setScreenshotUrl(null);
    }
  }), []);

  return (
    <div className={`relative w-full h-full rounded-2xl border border-black mb-9 overflow-hidden ${className || ''}`}>
      <Script src="https://js.arcgis.com/4.30/" strategy="beforeInteractive" />
      <div
        id="cpcViewDiv"
        style={{ height: height, width: "100%", display: screenshotUrl ? 'none' : 'block' }}
        className="pointer-events-none select-none"
      />
      {screenshotUrl && (
        <Image
          width={100}
          height={100}
          src={screenshotUrl}
          alt="Map screenshot"
          className="w-full h-[600px] object-cover pointer-events-none select-none block"
          style={{ zIndex: 1 }}
        />
      )}
    </div>
  );
});
MapInteractivePreview.displayName = 'MapInteractivePreview';
export default MapInteractivePreview;
