import { TableProps } from "@/lib/types";



const Table = ({
    columns,
    data,
    className = '',
    headerClassName = '',
    rowClassName = '',
    cellClassName = ''
}: TableProps) => {
    return (
        <div className={`overflow-hidden rounded-2xl border border-black ${className}`}>
            <table className="w-full border-collapse">
                {/* Table Header */}
                <thead>
                    <tr className={`bg-[#066058] text-white ${headerClassName} `}>
                        {columns.map((column) => (
                            <th
                                key={column.key}
                                className={`px-6 py-4 text-center text-base text-white leading-snug ${column.key === 'element' ? 'w-1/3' : 'w-2/3'
                                    } ${column.className || ''}`}
                            >
                                {column.label}
                            </th>
                        ))}
                    </tr>
                </thead>

                {/* Table Body */}
                <tbody>
                    {data.map((row, index) => (
                        <tr
                            key={index}
                            className={`border-b last:border-b-0 border-black bg-white hover:bg-[#f9fafb] ${rowClassName}`}
                        >
                            {columns.map((column) => (
                                <td
                                    key={`${index}-${column.key}`}
                                    className={`border-l last:border-l-0 px-6 py-4 text-center text-black text-base font-medium leading-snug ${column.key === 'element' ? 'w-1/3' : 'w-2/3'
                                        } ${cellClassName}`}
                                >
                                    {row[column.key]}
                                </td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>

            {/* Empty state */}
            {data.length === 0 && (
                <div className="px-6 py-12 text-center text-[#6b7280]">
                    لا توجد بيانات متاحة
                </div>
            )}
        </div>
    );
};

export default Table;
