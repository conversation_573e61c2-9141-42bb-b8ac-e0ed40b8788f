"use client"
import { SelectedPanelDetailsProps, SinglePanelsData } from '@/lib/types';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

const SelectedPanelDetails = ({ activePanel, panels, pdfMode = false }: SelectedPanelDetailsProps) => {
    const tCommon = useTranslations('Common');

    return (
        <>
            {activePanel ? (
                <div className={`my-20 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
                    <h4 className={`text-center text-md ${!pdfMode ? 'md:text-xl lg:text-2xl' : ''} font-bold text-black pb-5`}> اللوحة المختارة</h4>
                    <div className='rounded-lg'>
                        {/* Header */}
                        <h1 className={`text-base ${!pdfMode ? 'md:text-xl lg:text-2xl' : ''} font-bold text-primary mb-4`}>
                            {activePanel.title}
                        </h1>
                        <div className='relative'>
                            {/* Description */}
                            <div className="mb-4 ">
                                <p className="text-[#27272a] leading-normal font-medium text-base">
                                    <span className="text-[#0f766e] text-lg font-semibold mb-2">{tCommon("description")}</span>{activePanel.description}
                                </p>
                            </div>
                            {/* Purpose */}
                            <div className="mb-4 ">
                                <p className="text-[#27272a] leading-normal font-medium text-base">
                                    <span className="text-[#0f766e] text-lg font-semibold mb-2">{tCommon("purpose")}</span>{activePanel.purpose}
                                </p>
                            </div>
                            {/* General Rules */}
                            <div className="mb-4 ">
                                <ul className="space-y-2">
                                    {activePanel.generalRules.map((rule, index) => (
                                        <li key={index} className="text-[#27272a] flex items-start leading-normal font-medium text-base">
                                            <span className="text-[#0f766e] ml-2 mt-1">•</span>
                                            <span>{rule}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            {/* Colors */}
                            <div className="">
                                <h3 className="text-lg font-medium leading-normal text-[#0f766e]">{tCommon("colors")}</h3>
                                <ul className="space-y-2">
                                    {activePanel.colors.map((rule, index) => (
                                        <li key={index} className="text-[#27272a] flex items-start leading-normal font-medium text-base">
                                            <span className="text-[#0f766e] ml-2 mt-1">•</span>
                                            <span>{rule}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            {/* Lighting */}
                            <div className="mb-4 ">
                                <h3 className="text-lg font-medium leading-normal text-[#0f766e]">{tCommon("lights")}</h3>
                                <ul className="space-y-2">
                                    {activePanel.lighting.map((rule, index) => (
                                        <li key={index} className="text-[#27272a] flex items-start leading-normal font-medium text-base">
                                            <span className="text-[#0f766e] ml-2 mt-1">•</span>
                                            <span>{rule}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            {/* The Image part this position can be adjusted */}
                            <div className="flex justify-end absolute -bottom-10 left-2">
                                <Image src={activePanel.imageURL} alt="logo" width={200} height={200} />
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
                    <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-primary pb-5`}>تفاصيل اللوحة المختارة</h4>
                    <div className='bg-[#f9fafb] p-6 rounded-lg'>
                        <p className='text-center text-[#f9fafb]0'>لم يتم العثور على تفاصيل اللوحة المختارة</p>
                        <p className='text-center text-sm text-[#9ca3af] mt-2'>
                            الألواح المحفوظة: {panels.join(', ') || 'لا توجد'}
                        </p>
                    </div>
                </div>
            )}
        </>
    );
};

export default SelectedPanelDetails;
