"use client"
import { useScreenSize } from '@/context/ScreenSizeContext';
import { SelectedPanelDetailsProps, SinglePanelsData, TableColumn, TableRow } from '@/lib/types';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Table from './Table';

const SelectedPanelDetails = ({ activePanel, panels, pdfMode = false, refs }: SelectedPanelDetailsProps) => {
  const tCommon = useTranslations('Common');
  const { isMobile } = useScreenSize();

  const PLACEHOLDER_IMG = '/images/example1.png';

  return (
    <>
      {activePanel ? (
        <div className={`my-20 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
          <div ref={refs[1]} className={`${pdfMode ? "p-4" : ""}`}>
            <h4 className={`text-center text-md ${!pdfMode ? 'md:text-xl lg:text-2xl' : ''} font-bold text-black pb-5`}> اللوحة المختارة</h4>
            <div className='rounded-lg'>
              {/* Header */}
              <h1 className={`text-base ${!pdfMode ? 'md:text-xl lg:text-2xl' : ''} font-bold text-[#066058] mb-4`}>
                {activePanel.title}
              </h1>
              <div className='relative'>
                {/* Description */}
                <div className="mb-4 ">
                  <p className="text-[#27272a] leading-normal font-medium text-base">
                    <span className="text-[#0f766e] text-lg font-semibold mb-2">{tCommon("description")}</span>{activePanel.description}
                  </p>
                </div>
                {/* Purpose */}
                <div className="mb-4 ">
                  <p className="text-[#27272a] leading-normal font-medium text-base">
                    <span className="text-[#0f766e] text-lg font-semibold mb-2">{tCommon("generalConditions")}</span>{activePanel.purpose}
                  </p>
                </div>
                {/* General Rules */}
                <div className="mb-4 ">
                  <ul className="space-y-2">
                    {activePanel.generalRules.map((rule, index) => (
                      <li key={index} className="text-[#27272a] flex items-start leading-normal font-medium text-base">
                        <span className="text-[#0f766e] ml-2 mt-1">•</span>
                        <span>{rule}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                {/* Regulations */}
                {activePanel.regulations && activePanel.regulations.length > 0 && (
                  <div className="mb-4 ">
                    <h3 className="text-lg font-bold leading-normal text-[#0f766e] mb-3">{tCommon("regulations")}:</h3>
                    {activePanel.regulations.map((group, gi) => {
                      const entries = Object.entries(group.regulation || {});
                      const hasAny = entries.some(([, entry]: any) =>
                        (entry?.description && entry.description.trim().length > 0) ||
                        (entry?.allowed && entry.allowed.length > 0) ||
                        (entry?.notAllowed && entry.notAllowed.length > 0)
                      );
                      if (!hasAny) return null;
                      return (
                        <div key={gi} className="mb-4 border border-[#e5e7eb] rounded-lg p-3">
                          {group.name && group.name !== 'noTitle' && (
                            <h4 className="text-md font-semibold text-[#066058] mb-2">{group.name}</h4>
                          )}
                          {entries.map(([key, entry]: any, ei) => {
                            const hasAllowed = entry?.allowed && entry.allowed.length > 0;
                            const hasNotAllowed = entry?.notAllowed && entry.notAllowed.length > 0;
                            const hasDesc = entry?.description && entry.description.trim().length > 0;
                            if (!hasAllowed && !hasNotAllowed && !hasDesc) return null;
                            const showKeyTitle = isNaN(Number(key));
                            return (
                              <div key={ei} className="mb-3">
                                {showKeyTitle && (
                                  <div className="text-md font-bold text-[#0f766e] mb-1">{key}</div>
                                )}
                                {hasDesc && (
                                  <p className="text-[#27272a] leading-normal font-medium text-base mb-2">{entry.description}</p>
                                )}
                                <div className={`grid grid-cols-1 ${!hasNotAllowed || !hasAllowed ? '' : 'md:grid-cols-2'} gap-3`}>
                                  {hasNotAllowed && (
                                    <div className="rounded-md border border-[#fecaca] p-3">
                                      <div className="text-[#dc2626] font-semibold my-2 "><span className='p-2 rounded-md'>{tCommon("notAllowed")}</span></div>
                                      <ul className="space-y-2 flex">
                                        {entry.notAllowed.map((item: any, idx: number) => (
                                          <li key={idx} className="flex items-start gap-2">
                                            {item.description && <span className="text-[#ef4444] ml-2 mt-1">•</span>}
                                            <div className='flex flex-col justify-center items-center gap-5'>
                                              {item.description && <span className="text-[#27272a] leading-normal font-medium text-base">{item.description}</span>}
                                              <div className="mb-1">
                                                <Image src={item.imgSrc && item.imgSrc.trim() !== '' ? item.imgSrc : PLACEHOLDER_IMG} alt="not-allowed sample" width={300} height={150} className="rounded w-full" />
                                              </div>
                                            </div>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}
                                  {hasAllowed && (
                                    <div className="rounded-md border border-[#a7f3d0] p-3">
                                      <div className="text-[#047857] font-semibold my-2 "><span className=' p-2 rounded-md'>{tCommon("notAllowed")}</span></div>
                                      <ul className="space-y-2">
                                        {entry.allowed.map((item: any, idx: number) => (
                                          <li key={idx} className="flex items-start gap-2">
                                            {item.description && <span className="text-[#059669] ml-2 mt-1">•</span>}
                                            <div className='flex flex-col gap-5'>
                                              {item.description && <span className="text-[#27272a] leading-normal font-medium text-base">{item.description}</span>}
                                              <div className="mb-1">
                                                <Image src={item.imgSrc && item.imgSrc.trim() !== '' ? item.imgSrc : PLACEHOLDER_IMG} alt="allowed sample" width={300} height={150} className="rounded w-full" />
                                              </div>
                                            </div>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Standard Specs (Table) */}
          <div ref={refs[2]} className={`${pdfMode ? "p-4" : ""}`}>
            {activePanel.standardSpecs && (
              <div className="mb-4 ">
                <h3 className="text-lg font-medium leading-normal text-[#0f766e] mb-3">{tCommon("standardSpecs")}</h3>
                {(() => {
                  const columns: TableColumn[] = [
                    { key: 'element', label: 'العنصر' },
                    { key: 'details', label: 'التفاصيل' }
                  ];
                  const data: TableRow[] = Object.entries(activePanel.standardSpecs!).map(([key, value]) => ({
                    element: key,
                    details: value
                  }));
                  return (
                    <Table columns={columns} data={data} />
                  );
                })()}
              </div>
            )}
            {/* The Image part this position can be adjusted */}
            <div className={`${isMobile ? 'ltr' : ''} flex justify-center items-center w-full`}>
              <Image src={activePanel.imageURL || PLACEHOLDER_IMG} alt="panel" width={600} height={600} />
            </div>
          </div>
        </div>
      ) : (
        <div className={`my-6 px-4 ${!pdfMode ? 'sm:px-6 lg:px-8' : ''}`}>
          <h4 className={`text-sm ${!pdfMode ? 'md:text-md lg:text-xl' : ''} font-bold text-[#066058] pb-5`}>تفاصيل اللوحة المختارة</h4>
          <div className='bg-[#f9fafb] p-6 rounded-lg'>
            <p className='text-center text-[#f9fafb]0'>لم يتم العثور على تفاصيل اللوحة المختارة</p>
            <p className='text-center text-sm text-[#9ca3af] mt-2'>
              الألواح المحفوظة: {panels.join(', ') || 'لا توجد'}
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default SelectedPanelDetails;
