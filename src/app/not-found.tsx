import { useTranslations } from "next-intl";
import { Link } from "@/i18n/routing";

export const metadata = {
    title: '404 Not Found',
    description: 'The page you are looking for does not exist.'
};

export default function NotFound() {
    const t = useTranslations('404PageData');
    return (
        <div className="container flex flex-col items-center justify-center min-h-screen">
            <h1 className="text-6xl font-bold mb-4 text-primary">{t('title')}</h1>
            <p className="text-lg mb-8">
                {t('intro')}
            </p>
            <Link
                href="/"
                className="px-6 py-3 bg-primary text-white rounded-md font-medium hover:bg-primary/55 transition-colors"
            >
                {t('button')}
            </Link>
        </div>
    );
}

