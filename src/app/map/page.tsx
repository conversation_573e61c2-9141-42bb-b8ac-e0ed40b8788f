"use client"

import DropdownSection from "@/components/organisms/Dropdown";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import ZoomButton from '@/components/atoms/ZoomButton';

import Script from "next/script";
import Extent from "@arcgis/core/geometry/Extent";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import BasemapToggle from "./_components/BasemapToggle";
import { Button } from "@/components/atoms/Button";
import Header from "@/components/organisms/Header";

const MapPage = () => {
  const t = useTranslations('MapPage');
  const tCommon = useTranslations('Common');
  const viewMapRef = useRef<any>(null);
  const extentRef = useRef<any>(null);
  const router = useRouter();

  const mapImageLayerRef = useRef<any>(null);
  const featureLayerRef = useRef<any>(null);
  // keep a copy of initial options to restore when upstream filter is cleared
  const initialOptionsRef = useRef<{ planName: { value: string; label: string }[]; planNo: { value: string; label: string }[]; parcelNo: { value: string; label: string }[] }>({ planName: [], planNo: [], parcelNo: [] });
  const [municipalityOptions, setMunicipalityOptions] = useState<{ value: string; label: string }[]>([]);
  const [planNameOptions, setPlanNameOptions] = useState<{ value: string; label: string }[]>([]);
  const [planNoOptions, setPlanNoOptions] = useState<{ value: string; label: string }[]>([]);
  const [parcelNoOptions, setParcelNoOptions] = useState<{ value: string; label: string }[]>([]);
  const [municipality, setMunicipality] = useState('');
  const [planName, setPlanName] = useState('');
  const [planNo, setPlanNo] = useState('');
  const [parcelNo, setParcelNo] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isNextEnabled, setIsNextEnabled] = useState(parcelNo !== '' && parcelNo !== 'no_filter')
  // state for basemap
  const [currentBasemap, setCurrentBasemap] = useState<'topo' | 'satellite'>('topo');
  const [madinaExtent, setMadinaExtent] = useState<any>(null);
  const [selectedFeatureAttrs, setSelectedFeatureAttrs] = useState<any[] | null>(null);

  const clearAllFilters = () => {
    setMunicipality('');
    setPlanName('');
    setPlanNo('');
    setParcelNo('');
  };

  // console.log(`municipality is ${municipality}`)
  // console.log(`planName is ${planName}`)
  // console.log(`planNo is ${planNo}`)
  // console.log(`parcelNo is ${parcelNo}`)

  useEffect(() => {
    (window as any).require(
      ["esri/Map", "esri/views/MapView", "esri/layers/MapImageLayer",
        "esri/geometry/Extent", "esri/layers/FeatureLayer"
      ],
      (Map: any, MapView: any, MapImageLayer: any, Extent: any, FeatureLayer: any) => {
        const map = new Map({ basemap: "topo-vector" });

        const layer = new MapImageLayer({
          url: "https://namaa-gis.kharetatalenmaa.sa/server/rest/services/MadinaPart_MIL1/MapServer",
        });
        map.add(layer);

        const viewMap = new MapView({
          container: "viewDiv",
          map: map,
          center: [39.6, 24.47],
          zoom: 12,
        });

        const container = document.getElementById("viewDiv");
        if (container) {
          container.style.borderRadius = "20px";
          container.style.overflow = "hidden";
        }

        viewMapRef.current = viewMap;
        mapImageLayerRef.current = layer;
        extentRef.current = Extent;

        // Add click event listener for map interaction
        viewMap.on("click", (event: any) => {
          handleMapClick(event, FeatureLayer);
        });

        // store initial madina extent to reset later
        viewMap.when(() => {
          layer.when(() => {
            if (layer.fullExtent) {
              // Al-Masjid an-Nabawi center
              const lat = 24.4672;
              const lon = 39.6111;
              const ex = new Extent({
                xmin: lon - 0.01,
                ymin: lat - 0.01,
                xmax: lon + 0.01,
                ymax: lat + 0.01,
                spatialReference: { wkid: 4326 }  // WGS84
              });

              setMadinaExtent(ex.expand(.6));
            }
          });
        });

        // query dropdown values
        directFeatureLayer();
      }
    );
  }, []);

  // Create FeatureLayer directly from service URL using CDN
  const directFeatureLayer = () => {
    (window as any).require(["esri/layers/FeatureLayer"], (FeatureLayer: any) => {
      const directFeatureLayer = new FeatureLayer({
        url: "https://namaa-gis.kharetatalenmaa.sa/server/rest/services/MadinaPart_MIL1/MapServer/1",
      });
      featureLayerRef.current = directFeatureLayer;
      fetchDropdownOptions(directFeatureLayer);
    });
  };
  // Fetch dropdown options using getField() to handle coded value domains
  const fetchDropdownOptions = async (featureLayer: any) => {
    try {
      setIsLoading(true);
      // console.log("Fetching dropdown options using getField()...");

      // Define the fields you want to fetch options for
      const fieldsToFetch = ["Municapility", "PLAN_NAME", "PLAN_NO", "PARCELNO"];

      // Ensure the FeatureLayer is loaded
      if (!featureLayer.loaded) {
        await featureLayer.load();
      }

      // Create queries for each field
      const queries = fieldsToFetch.map((fieldName) => {
        const query = featureLayer.createQuery();
        query.outFields = [fieldName];
        query.returnDistinctValues = true;
        query.where = `${fieldName} IS NOT NULL AND ${fieldName} <> ''`;
        query.returnGeometry = false;
        return { fieldName, query };
      });

      // Execute all queries in parallel
      const results = await Promise.all(
        queries.map(({ query }) => featureLayer.queryFeatures(query))
      );

      // Process results and update state
      results.forEach((result: any, index: number) => {
        const fieldName = queries[index].fieldName;

        // Check if the field has a domain with coded values
        const field = featureLayer.getField(fieldName);
        const codedValues =
          field?.domain && field.domain.type === "coded-value"
            ? (field.domain as __esri.CodedValueDomain).codedValues
            : null;

        // Store municipality domain mapping for use in other pages
        if (fieldName === "Municapility" && codedValues) {
          const municipalityMapping: Record<string, string> = {};
          codedValues.forEach((cv: any) => {
            municipalityMapping[cv.code] = cv.name;
          });
          if (typeof window !== 'undefined') {
            window.sessionStorage.setItem('municipality_domain_mapping', JSON.stringify(municipalityMapping));
          }
        }

        const options = result.features
          .map((feature: any) => {
            const value = feature.attributes[fieldName];
            const label =
              fieldName === "Municapility" && codedValues
                ? codedValues.find((cv: any) => cv.code === value)?.name || value
                : value;

            return {
              value: String(value).trim(),
              label: String(label).trim(),
            };
          })
          .filter((option: any) => option.value != null && option.value !== "")
          .sort((a: any, b: any) => a.label.localeCompare(b.label));

        // Ensure unique keys to avoid duplicate key errors
        const uniqueOptions = options.filter(
          (option: any, index: number, array: any[]) =>
            array.findIndex((item) => item.value === option.value) === index
        );


        // This ensures users can always select "no filter" to remove filtering for specific fields
        const optionsWithNoFilter = [
          { value: 'no_filter', label: 'بدون تصفية' }, // Special value to indicate no filtering
          ...uniqueOptions
        ];

        // console.log(`Found ${options.length} options for ${fieldName}`);

        // Update state based on the field name with "no filter" option included
        if (fieldName === "Municapility") setMunicipalityOptions(optionsWithNoFilter);
        if (fieldName === "PLAN_NAME") {
          setPlanNameOptions(optionsWithNoFilter);
          if (initialOptionsRef.current.planName.length === 0) initialOptionsRef.current.planName = optionsWithNoFilter;
        }
        if (fieldName === "PLAN_NO") {
          setPlanNoOptions(optionsWithNoFilter);
          if (initialOptionsRef.current.planNo.length === 0) initialOptionsRef.current.planNo = optionsWithNoFilter;
        }
        if (fieldName === "PARCELNO") {
          setParcelNoOptions(optionsWithNoFilter);
          if (initialOptionsRef.current.parcelNo.length === 0) initialOptionsRef.current.parcelNo = optionsWithNoFilter;
        }
      });

      // console.log("Dropdown options updated successfully.");
    } catch (error) {
      console.error("Error fetching dropdown options:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced apply filters with auto-zoom functionality
  const applyFilters = useCallback(async () => {
    setIsNextEnabled(false)
    const mapImageLayer = mapImageLayerRef.current;

    const view = viewMapRef.current;
    const featureLayer = featureLayerRef.current;

    if (!mapImageLayer || !view) {
      return;
    }

    // Target the correct sublayer id used by FeatureLayer (/1)
    const targetLayerId = 1;
    const sublayer = mapImageLayer.sublayers.find((sl: any) => sl?.id === targetLayerId);

    if (!sublayer) {
      //console.log("No sublayer found");
      return;
    }


    const fieldMappings = [
      { value: municipality, fieldName: 'Municapility' },
      { value: planName, fieldName: 'PLAN_NAME' },
      { value: planNo, fieldName: 'PLAN_NO' },
      { value: parcelNo, fieldName: 'PARCELNO' }
    ];

    // Only add filter conditions for fields that have actual values (not "no_filter" or empty)
    const filters = fieldMappings
      .filter(({ value }) => value && value !== 'no_filter' && value !== '')
      .map(({ value, fieldName }) => `${fieldName} = '${value}'`);


    // Apply the definition expression (SQL-like filter)
    const definitionExpression = filters.length > 0 ? filters.join(" AND ") : "1=1";
    sublayer.definitionExpression = definitionExpression;
    // console.log("Applied filters:", definitionExpression);

    // zoom to extent of filtered features
    const query = featureLayer.createQuery();
    query.where = definitionExpression;

    try {
      const [extentResult, featureResult] = await Promise.all([
        featureLayer.queryExtent(query),
        featureLayer.queryFeatures({ where: definitionExpression, outFields: ["*"], returnGeometry: false })
      ]);

      // Capture attributes for all matching features
      try {
        const features = featureResult?.features || [];
        const attrsList = features.map((f: any) => f?.attributes);
        // console.log(`attrsList are ${JSON.stringify(attrsList)}`)
        setSelectedFeatureAttrs(attrsList.length > 0 ? attrsList : null);
      } catch { }

      if (extentResult.extent) {
        const Extent = extentRef.current;

        const ex = new Extent({
          xmin: extentResult.extent.xmin,
          ymin: extentResult.extent.ymin,
          xmax: extentResult.extent.xmax,
          ymax: extentResult.extent.ymax,
          spatialReference: view.spatialReference
        });

        const expanded = ex.expand(1.5);
        // Use simple goTo to avoid animation-related runtime errors
        await view.goTo(definitionExpression == "1=1" ? madinaExtent : expanded).catch(() => { });
        setIsNextEnabled(parcelNo !== '' && parcelNo !== 'no_filter')
        // Store map state for CPC preview instead of static images
        try {
          const expandedToPrint = ex.expand(3);
          const mapState = {
            center: [view.center.longitude, view.center.latitude],
            extent: {
              xmin: expandedToPrint.xmin,
              ymin: expandedToPrint.ymin,
              xmax: expandedToPrint.xmax,
              ymax: expandedToPrint.ymax,
              spatialReference: view.spatialReference?.wkid || 102100
            },
            definitionExpression,
            targetLayerId,
            zoom: view.zoom,
            basemap: currentBasemap
          };

          // Store coordinates for report page display
          const coordinates = {
            center: [view.center.longitude, view.center.latitude],
            extent: {
              xmin: ex.xmin,
              ymin: ex.ymin,
              xmax: ex.xmax,
              ymax: ex.ymax
            }
          };

          if (typeof window !== 'undefined') {
            window.sessionStorage.setItem('cpc_map_state', JSON.stringify(mapState));
            window.sessionStorage.setItem('selected_coordinates', JSON.stringify(coordinates));
          }
          //console.log('Map state stored:', mapState);
        } catch (error) {
          console.warn('Failed to store map state:', error);
        }
      }
      else {
        await view.goTo(madinaExtent, { duration: 1000, easing: "ease-in-out" });
      }
    } catch (err) {
      console.error("Error zooming:", err);
      //console.log("no");

    }

  }, [municipality, planName, planNo, parcelNo, madinaExtent]);
  // Apply filters whenever dropdown values change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Dependent dropdowns: PLAN_NAME depends on municipality; PLAN_NO depends on planName; PARCELNO depends on planNo
  useEffect(() => {
    // PLAN_NAME depends on municipality; also apply safeguard filter when municipality selected
    const hasMunicipality = municipality && municipality !== 'no_filter' && municipality !== '';
    const basePlanName = initialOptionsRef.current.planName.length > 0 ? initialOptionsRef.current.planName : planNameOptions;
    if (!hasMunicipality) {
      // restore original planName options
      if (basePlanName.length > 0) setPlanNameOptions(basePlanName);
      return;
    }

    // Filter PLAN_NAME options by current municipality using selectedFeatureAttrs snapshot
    if (Array.isArray(selectedFeatureAttrs) && selectedFeatureAttrs.length > 0) {
      try {
        const setNames = new Set<string>();
        for (const attrs of selectedFeatureAttrs) {
          if (String(attrs?.Municapility) === String(municipality)) {
            const name = attrs?.PLAN_NAME;
            if (name != null && String(name).trim() !== '') setNames.add(String(name).trim());
          }
        }
        const derived = Array.from(setNames).sort((a, b) => a.localeCompare(b)).map(v => ({ value: v, label: v }));
        const withNoFilter = [{ value: 'no_filter', label: 'بدون تصفية' }, ...derived];
        if (derived.length > 0) setPlanNameOptions(withNoFilter);
      } catch { }
    }
  }, [municipality, selectedFeatureAttrs]);

  useEffect(() => {
    // PLAN_NO depends on PLAN_NAME
    const hasPlanName = planName && planName !== 'no_filter' && planName !== '';
    const basePlanNo = initialOptionsRef.current.planNo.length > 0 ? initialOptionsRef.current.planNo : planNoOptions;
    if (!hasPlanName) {
      if (basePlanNo.length > 0) setPlanNoOptions(basePlanNo);
      return;
    }
    if (Array.isArray(selectedFeatureAttrs) && selectedFeatureAttrs.length > 0) {
      try {
        const setNos = new Set<string>();
        for (const attrs of selectedFeatureAttrs) {
          if (String(attrs?.PLAN_NAME) === String(planName)) {
            const v = attrs?.PLAN_NO;
            if (v != null && String(v).trim() !== '') setNos.add(String(v).trim());
          }
        }
        const derived = Array.from(setNos).sort((a, b) => a.localeCompare(b)).map(v => ({ value: v, label: v }));
        const withNoFilter = [{ value: 'no_filter', label: 'بدون تصفية' }, ...derived];
        if (derived.length > 0) setPlanNoOptions(withNoFilter);
      } catch { }
    }
  }, [planName, selectedFeatureAttrs]);

  useEffect(() => {
    // PARCELNO depends on PLAN_NO
    const hasPlanNo = planNo && planNo !== 'no_filter' && planNo !== '';
    const baseParcelNo = initialOptionsRef.current.parcelNo.length > 0 ? initialOptionsRef.current.parcelNo : parcelNoOptions;
    if (!hasPlanNo) {
      if (baseParcelNo.length > 0) setParcelNoOptions(baseParcelNo);
      return;
    }
    if (Array.isArray(selectedFeatureAttrs) && selectedFeatureAttrs.length > 0) {
      try {
        const setParcels = new Set<string>();
        for (const attrs of selectedFeatureAttrs) {
          if (String(attrs?.PLAN_NO) === String(planNo)) {
            const v = attrs?.PARCELNO;
            if (v != null && String(v).trim() !== '') setParcels.add(String(v).trim());
          }
        }
        const derived = Array.from(setParcels).sort((a, b) => a.localeCompare(b)).map(v => ({ value: v, label: v }));
        const withNoFilter = [{ value: 'no_filter', label: 'بدون تصفية' }, ...derived];
        if (derived.length > 0) setParcelNoOptions(withNoFilter);
      } catch { }
    }
  }, [planNo, selectedFeatureAttrs]);


  useEffect(() => {
    if (municipality === 'no_filter') {
      setPlanName((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
      setPlanNo((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
      setParcelNo((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
    }
  }, [municipality]);

  useEffect(() => {
    if (planName === 'no_filter') {
      setPlanNo((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
      setParcelNo((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
    }
  }, [planName]);

  useEffect(() => {
    if (planNo === 'no_filter') {
      setParcelNo((prev) => (prev === 'no_filter' ? prev : 'no_filter'));
    }
  }, [planNo]);

  // Handle zoom in and zoom out actions
  const handleZoom = async (delta: any) => {
    const view = viewMapRef.current;
    if (!view) {
      //console.log("No view available");
      return;
    }

    const currentZoom = view.zoom;
    const newZoom = currentZoom + delta;

    await view.goTo({ zoom: newZoom }, { duration: 300 });
  };

  // Toggle basemap function
  const toggleBasemap = () => {
    const view = viewMapRef.current;
    if (!view) return;

    const newBasemap = currentBasemap === 'topo' ? 'satellite' : 'topo';
    const basemapId = newBasemap === 'topo' ? 'topo-vector' : 'satellite';

    view.map.basemap = basemapId;
    setCurrentBasemap(newBasemap);

    //console.log(`Switched to ${newBasemap} basemap`);
  };

  // Get user's current location
  const getUserLocation = async () => {
    const view = viewMapRef.current;
    if (!view) {
      //console.log("No view available");
      return;
    }

    try {
      if (!navigator.geolocation) {
        console.log("Geolocation is not supported by this browser");
        return;
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        });
      });

      const { latitude, longitude } = position.coords;

      await view.goTo({
        center: [longitude, latitude],
        zoom: 16
      }, {
        duration: 1000,
        easing: 'ease-in-out'
      });

      console.log("Moved to user location:", { latitude, longitude });
    } catch (error) {
      console.error("Error getting user location:", error);
      // Fallback to Madina extent if location fails
      if (madinaExtent) {
        await view.goTo(madinaExtent, {
          duration: 1000,
          easing: 'ease-in-out'
        });
      }
    }
  };

  const handelNextStep = () => {
    try {
      if (typeof window !== 'undefined') {
        // window.sessionStorage.setItem('selected_condition', selectedCondition || '');
        window.sessionStorage.setItem('selected_feature_attrs', Array.isArray(selectedFeatureAttrs) ? JSON.stringify(selectedFeatureAttrs) : '');
      }
    } catch { }
    // console.log('saving', JSON.stringify(selectedFeatureAttrs));
    router.push('/types-of-panels-allowed');
  }

  // Handle map click to identify and filter features
  const handleMapClick = async (event: any, FeatureLayer: any) => {
    const view = viewMapRef.current;
    const featureLayer = featureLayerRef.current;

    if (!view || !featureLayer) return;

    try {
      // Create a point from the click event
      const point = event.mapPoint;
      // console.log(`point selected is : ${JSON.stringify(point)}`);

      // Create a query to find features at the clicked location
      const query = featureLayer.createQuery();
      query.geometry = point;
      query.spatialRelationship = "intersects";
      query.outFields = ["Municapility", "PLAN_NAME", "PLAN_NO", "PARCELNO"];
      query.returnGeometry = false;

      const result = await featureLayer.queryFeatures(query);

      if (result.features && result.features.length > 0) {
        const feature = result.features[0];
        const attrs = feature.attributes;

        // console.log(`Feature clicked: ${JSON.stringify(result.features)}`);
        // console.log(`attrs selected: ${JSON.stringify(attrs)}`);

        // Update dropdown selections based on clicked feature
        if (attrs.Municapility) {
          setMunicipality(String(attrs.Municapility).trim());
        }
        if (attrs.PLAN_NAME) {
          setPlanName(String(attrs.PLAN_NAME).trim());
        }
        if (attrs.PLAN_NO) {
          setPlanNo(String(attrs.PLAN_NO).trim());
        }
        if (attrs.PARCELNO) {
          setParcelNo(String(attrs.PARCELNO).trim());
        }

        //console.log("Feature clicked:", attrs);
      } else {
        //console.log("No features found at clicked location");
      }
    } catch (error) {
      console.error("Error handling map click:", error);
    }
  };

  // Render the MapPage component
  return (
    <main className="pt-15 min-h-screen">
      <Header />
      <div className=" px-10 mx-auto ">
        {/* Intro */}
        <div className="flex flex-row gap-3 bg-forthBackground p-2.5 rounded-lg w-full md:w-2/3 my-6">
          <Image src="/images/saveIcon.svg" alt="saveLogo" width={10} height={10} className="w-6 h-6 flex-shrink-0" />
          <span className="text-sm md:text-base">{t("intro")}</span>
        </div>

        {/* Loading indicator */}
        {isLoading && (
          <div className="text-center py-4">
            <span className="text-sm text-gray-600">{t("loadingFilters")}</span>
          </div>
        )}

        {/* Dropdown sections */}
        <DropdownSection
          municipality={municipality}
          setMunicipality={setMunicipality}
          municipalityOptions={municipalityOptions}
          planName={planName}
          setPlanName={setPlanName}
          planNameOptions={planNameOptions}
          planNo={planNo}
          setPlanNo={setPlanNo}
          planNoOptions={planNoOptions}
          parcelNo={parcelNo}
          setParcelNo={setParcelNo}
          parcelNoOptions={parcelNoOptions}
        />

        <div className="flex gap-3 mb-4 h-11">
          <Button
            variant="outline"
            onClick={clearAllFilters}
            className="!border-gray-300 !text-gray-600 hover:!bg-gray-50"
          >
            <span>{tCommon("clearAllFilters")}</span>
          </Button>

          <Button variant="outline3" onClick={handelNextStep} disabled={!isNextEnabled} className={`!bg-primary !border-navHover`}>
            <span>{tCommon("nextStep")}</span>
            <Image src="/images/arrow2.svg" alt="arrow icon" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
          </Button>
        </div>

        {/* Map section */}
        <div className="mt-2 mb-4 border border-[#D0D5DD] rounded-2xl overflow-hidden">
          <Script src="https://js.arcgis.com/4.30/" strategy="beforeInteractive" />
          <div className="flex justify-between items-center bg-primary p-1">
            <div
              className="relative"
              id="viewDiv"
              style={{
                height: "65vh",
                width: "100%",
                // Ensure map captures touch and blocks page scroll chaining on mobile
                touchAction: "none",
                overscrollBehavior: "contain",
              }}
            >
              {/* Zoom Controls */}
              <ZoomButton
                className="w-fit absolute top-2 right-2"
                onZoomIn={() => handleZoom(+1)}
                onZoomOut={() => handleZoom(-1)}
                onLocation={() => getUserLocation()}
              />

              {/* Basemap Toggle */}
              <div className="absolute bottom-2 right-2 ">
                <BasemapToggle toggleBasemap={toggleBasemap} currentBasemap={currentBasemap} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default MapPage;
