import { Antenna } from 'lucide-react'
import { Satellite } from 'lucide-react'
import Image from "next/image";

type BasemapToggleProps = {
    toggleBasemap: () => void;
    currentBasemap: "topo" | "satellite"
}

const BasemapToggle = ({ toggleBasemap, currentBasemap }: BasemapToggleProps) => {
    return (
        <button
            onClick={toggleBasemap}
            className="cursor-pointer px-4 py-3 bg-white/30 rounded-[40px] backdrop-blur-lg inline-flex flex-row-reverse justify-center items-center gap-2.5"
            title={`Switch to ${currentBasemap === 'topo' ? 'Satellite' : 'Topographic'} view`}
        >
            {/* {currentBasemap === 'topo' ? <Satellite className="w-6 h-6 relative" /> : <Antenna className="w-6 h-6 relative" />} */}
            <Image src="/images/baseMapIcon.svg" alt="saveLogo" width={8} height={8} className="w-7 h-7 flex-shrink-0" />
            <span className="justify-start text-black text-base font-normal font-['Inter'] leading-loose">
                {currentBasemap === 'topo' ? 'قمر صناعي' : 'طبوغرافي'}
            </span>
        </button>
    )
}

export default BasemapToggle