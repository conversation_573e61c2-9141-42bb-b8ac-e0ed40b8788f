import type { Metada<PERSON> } from "next";
import localFont from 'next/font/local';
import { NextIntlClientProvider } from 'next-intl';
import ar from '@/data/ar';
import "./globals.css";

const sansArabic = localFont({
    src: [
        {
            path: '../../public/fonts/TheSansArabic/TheSansArabic-ExtraLight.otf',
            weight: '200',
        },
        {
            path: '../../public/fonts/TheSansArabic/TheSansArabic-Light.otf',
            weight: '300',
        },
        {
            path: '../../public/fonts/TheSansArabic/TheSansArabic_Plain.ttf',
            weight: '400',
        },
        {
            path: '../../public/fonts/TheSansArabic/TheSansArabic-BOLD.ttf',
            weight: '700',
        }
    ],
    variable: '--sans-arabic',
    display: 'swap',
});

export const metadata: Metadata = {
    title: "MadinaMap",
    description: "Generated by create next app",
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="ar" dir="rtl">
            <body
                className={`antialiased min-h-screen ${sansArabic.variable} ${sansArabic.className}`}
                suppressHydrationWarning
            >
                <NextIntlClientProvider locale="ar" messages={ar}>
                    {children}
                </NextIntlClientProvider>

            </body>
        </html>
    );
}