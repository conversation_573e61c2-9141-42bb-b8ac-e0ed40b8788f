'use client'

import { forwardRef, useState } from 'react';

interface BaseProps {
    label: string;
    error?: boolean;
    fullWidth?: boolean;
}

interface InputProps extends BaseProps, Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
    type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
    name: string; 
}

interface TextareaProps extends BaseProps, React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    type: 'textarea';
    name: string; 
}

type ArabicInputProps = InputProps | TextareaProps;

const Input = forwardRef<HTMLInputElement | HTMLTextAreaElement, ArabicInputProps>(
    ({ label, name, error, fullWidth = false, className = '', ...props }, ref) => {
        const [value, setValue] = useState('');

        const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            setValue(e.target.value);
        };

        const baseClasses = `
        cursor-text w-full px-4 py-3 border
        ${error ? 'border-textError focus:ring-red-200' : 'border-transparent'}
        bg-inputBackground rounded-xl text-neutral-500 placeholder-neutral-500
        focus:outline-none focus:ring-inputBackground transition-all duration-200 text-right
        ${className}
        `.trim();

        const commonProps = {
            name,
            id: name,
            className: baseClasses,
            'aria-required': true,
            'aria-invalid': !!error,
            autoComplete: 'on',
            value,
            onChange: handleChange,
        };

        return (
            <div className={`${fullWidth ? 'w-full' : 'w-1/2'} el-messiri inline-block p-2`}>
                {/* Label */}
                <label className="block text-formText text-sm font-medium mb-2 text-right" htmlFor={name}>
                    {label}
                </label>

                {/* Input or Textarea */}
                {props.type === 'textarea' ? (
                    <textarea
                        ref={ref as React.ForwardedRef<HTMLTextAreaElement>}
                        {...commonProps}
                        {...(props as TextareaProps)}
                    />
                ) : (
                    <input
                        type={props.type || 'text'}
                        ref={ref as React.ForwardedRef<HTMLInputElement>}
                        {...commonProps}
                        {...(props as InputProps)}
                    />
                )}
            </div>
        );
    }
);

Input.displayName = 'ArabicInput';

export default Input;