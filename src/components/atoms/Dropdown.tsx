'use client'

import { DropdownOption } from '@/lib/types';
import React, { useEffect, useState, useRef } from 'react';
import { ChevronDown } from 'lucide-react';

type DropdownProps = {
    label: string;
    placeholder: string;
    options: DropdownOption[];
    value?: string;
    onChange?: (value: string) => void;
    className?: string;
    disabled?: boolean;
    searchable?: boolean;
}

const Dropdown = ({
    label,
    placeholder,
    options,
    value,
    onChange,
    className = "",
    disabled = false,
    searchable = false,
}: DropdownProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || "");
    const [searchTerm, setSearchTerm] = useState("");
    const [filteredOptions, setFilteredOptions] = useState(options);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const menuRef = useRef<HTMLDivElement>(null);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    // Sync internal state when external value changes
    useEffect(() => {
        setSelectedValue(value || "");
    }, [value]);

    // Filter options based on search term
    useEffect(() => {
        if (!searchable) {
            setFilteredOptions(options);
            return;
        }

        if (searchTerm.trim() === "") {
            setFilteredOptions(options);
        } else {
            const filtered = options.filter(option =>
                option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
                option.value.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredOptions(filtered);
        }
    }, [searchTerm, options, searchable]);

    // Close dropdown when clicking outside
    useEffect(() => {
        if (!searchable) return;

        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
                setSearchTerm("");
                setHighlightedIndex(-1);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [searchable]);

    const handleSelect = (optionValue: string) => {
        setSelectedValue(optionValue);
        setIsOpen(false);
        if (searchable) setSearchTerm("");
        onChange?.(optionValue);
    };
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!isOpen) return;

        if (e.key === "ArrowDown") {
            e.preventDefault();
            setHighlightedIndex((prev) =>
                prev < filteredOptions.length - 1 ? prev + 1 : 0
            );
        } else if (e.key === "ArrowUp") {
            e.preventDefault();
            setHighlightedIndex((prev) =>
                prev > 0 ? prev - 1 : filteredOptions.length - 1
            );
        } else if (e.key === "Enter" && highlightedIndex >= 0) {
            e.preventDefault();
            const selectedOption = filteredOptions[highlightedIndex];
            setSelectedValue(selectedOption.value);
            setIsOpen(false);
            setSearchTerm("");
            setHighlightedIndex(-1);
            onChange?.(selectedOption.value);
        } else if (e.key === "Escape") {
            setIsOpen(false);
            setHighlightedIndex(-1);
            setSearchTerm("");
        }
    };

    const selectedOption = options.find(option => option.value === selectedValue);

    // Auto-scroll highlighted option into view
    useEffect(() => {
        if (highlightedIndex >= 0 && menuRef.current) {
            const menu = menuRef.current;
            const highlightedElement = menu.children[highlightedIndex] as HTMLElement;

            if (highlightedElement) {
                const menuRect = menu.getBoundingClientRect();
                const elementRect = highlightedElement.getBoundingClientRect();

                if (elementRect.bottom > menuRect.bottom) {
                    // Scroll down
                    menu.scrollTop += elementRect.bottom - menuRect.bottom;
                } else if (elementRect.top < menuRect.top) {
                    // Scroll up
                    menu.scrollTop -= menuRect.top - elementRect.top;
                }
            }
        }
    }, [highlightedIndex]);

    return (
        <div className={`w-full  ${className}`} dir="rtl" ref={dropdownRef}>
            {/* Label */}
            <label className="block text-slate-700 text-base leading-snug font-medium mb-3 text-right">
                {label}
            </label>

            {/* Dropdown Container */}
            <div className="relative">
                {searchable ? (
                    // Searchable Input
                    <input
                        ref={inputRef}
                        type="text"
                        value={isOpen ? searchTerm : (selectedOption?.label || "")}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onFocus={() => setIsOpen(true)}
                        onKeyDown={handleKeyDown}
                        placeholder={placeholder}
                        disabled={disabled}
                        autoComplete="on"
                        className={`
                            w-full px-4 py-2 text-right text-lg bg-white border border-gray-300 rounded-2xl
                            focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                            disabled:bg-gray-100 disabled:cursor-not-allowed
                            ${disabled ? 'text-gray-400' : 'text-slate-700'}
                        `}
                    />
                ) : (
                    // Regular Button
                    <button
                        type="button"
                        onClick={() => !disabled && setIsOpen(!isOpen)}
                        disabled={disabled}
                        className={`
                            w-full bg-white border border-[#D0D5DD] rounded-2xl px-4 text-right
                            flex items-center justify-between min-h-[50px]
                            transition-all duration-200 ease-in-out
                            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 cursor-pointer'}
                        `}
                    >
                        <span className={`text-lg ${selectedOption ? 'text-slate-700' : 'text-neutral-500 text-base font-light'}`}>
                            {selectedOption ? selectedOption.label : placeholder}
                        </span>

                        {/* Dropdown Arrow */}
                        <ChevronDown className={`w-6 h-6 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''
                            }`} />
                    </button>
                )}

                {/* Dropdown Menu */}
                {isOpen && (
                    <div
                        ref={menuRef}
                        className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-2xl shadow-lg max-h-60 overflow-y-auto scrollbar-hide"
                    >
                        {filteredOptions.length > 0 ? (
                            filteredOptions.map((option, index) => (
                                <button
                                    key={option.value}
                                    onClick={() => handleSelect(option.value)}
                                    className={`cursor-pointer
                                        w-full px-4 py-2 text-right text-lg hover:bg-formBackground transition-colors duration-150
                                        ${index === 0 ? 'rounded-t-2xl' : ''}
                                        ${index === filteredOptions.length - 1 ? 'rounded-b-2xl' : ''}
                                        ${selectedValue === option.value ? 'bg-formBackground text-primary' : 'text-slate-700'}
                                        ${index === highlightedIndex ? 'bg-formBackground' : ''}
                                    `}
                                >
                                    {option.label}
                                </button>
                            ))
                        ) : (
                            <div className="px-4 py-2 text-center text-gray-500">
                                لا توجد نتائج
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Backdrop to close dropdown when clicking outside */}
            {isOpen && !searchable && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => setIsOpen(false)}
                />
            )}
        </div>
    );
};

export default Dropdown;
