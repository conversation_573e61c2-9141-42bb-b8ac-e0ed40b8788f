import { ArrowLeft, ArrowRight } from 'lucide-react';

type NavigationButtonsProps = {
    onPrevious?: () => void;
    onNext?: () => void;
    disablePrevious?: boolean;
    disableNext?: boolean;
    className?: string;
}
//  aria-label="Previous slide"
export default function NavigationButtons({
    onPrevious,
    onNext,
    disablePrevious = false,
    disableNext = false,
    className = '',
}: NavigationButtonsProps) {
    return (
        <div className={`flex items-center gap-3 ${className}`}>
            {/* Next Button */}
            <button
                onClick={onNext}
                disabled={disableNext}
                className="cursor-pointer w-12 h-12 rounded-full border-1 border-primary bg-border-primary hover:bg-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center "
            >
                <ArrowRight className="w-6 h-6 text-background" />
            </button>
            {/* Previous Button */}
            <button
                onClick={onPrevious}
                disabled={disablePrevious}
                className="group cursor-pointer w-12 h-12 rounded-full border-1 border-black  hover:border-primary bg-background hover:bg-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center"
            >
                <ArrowLeft className="w-6 h-6 text-black group-hover:text-background" />
            </button>
        </div>
    );
}