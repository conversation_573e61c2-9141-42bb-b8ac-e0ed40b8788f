import React from 'react';
import Image from "next/image";

type ButtonProps = {
    key?: string;
    ariaLabel?: string;
    className?: string;
    variant?: 'primary' | 'secondary' | 'outline' | 'outline2' | 'outline3' | 'gradient';
    hasArrow?: boolean;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
    children?: React.ReactNode;
    icon?: React.ReactNode;
    subtitle?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({
        className = '',
        variant = 'primary',
        hasArrow = false,
        children,
        icon,
        subtitle,
        ...props
    }, ref) => {
        // Base button styles
        const baseClasses = "group relative inline-flex items-center justify-center font-medium transition-all duration-300 overflow-hidden focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none cursor-pointer px-3 py-1 text-xs xl:px-6 xl:py-3 xl:text-sm";

        // Variant styles
        const variantClasses = {
            primary: "text-white bg-black hover:bg-white hover:text-black rounded-[100px] border border-black",
            secondary: "bg-white hover:bg-primary text-black hover:text-white border border-gray-300 rounded-full",
            outline: "bg-transparent hover:bg-gray-50 text-gray-700 border border-[#0F0F0F1A] hover:border-black rounded-full",
            outline2: "bg-transparent hover:bg-gradient-to-r hover:from-[var(--gradient-primary-start)] hover:to-[var(--gradient-primary-end)] text-border-primary border border-border-primary rounded-full hover:text-white",
            outline3: "bg-transparent hover:bg-navHover text-white border border-white hover:border-primary rounded-full",
            gradient: "bg-gradient-to-r from-[var(--gradient-secondary-start)] to-[var(--gradient-secondary-end)] hover:from-[var(--gradient-secondary-end)] hover:to-[var(--gradient-secondary-start)] text-white rounded-full shadow-lg"
        };

        // Combine all classes
        const buttonClasses = [
            baseClasses,
            variantClasses[variant],
            className
        ].join(' ');

        return (
            <button
                ref={ref}
                className={buttonClasses}
                {...props}
            >
                <div className="flex items-center justify-center relative z-10 w-full">
                    {/* Content container */}
                    <div className={`flex flex-col items-center flex-grow`}>
                        <span className="font-semibold flex flex-row justify-between items-center">
                            {hasArrow && (
                                <div className="relative w-9 h-9">
                                    <Image src="/images/arrow1.svg" 
                                    alt="arrow" 
                                    width={100}
                                    height={100}                                    className="transition-opacity duration-300 " />
                                </div>
                            )}
                            {children}
                        </span>
                        {subtitle && (
                            <span className="text-xs opacity-75 text-left text-gray group-hover:text-text1 ps-8">
                                {subtitle}
                            </span>
                        )}
                        {/* Icon container */}

                    </div>
                    {icon && (
                        <div className="w-9 h-9 rounded-full bg-neutral-200 flex items-center justify-center mr-3 group-hover:bg-white">
                            {icon}
                        </div>
                    )}
                </div>
            </button>
        );
    }
);

Button.displayName = 'Button';

export { Button };
