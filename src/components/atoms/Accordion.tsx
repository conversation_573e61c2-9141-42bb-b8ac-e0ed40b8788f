import { Minus, Plus } from "lucide-react";
import { ReactNode } from "react";
import React from "react";

type AccordionProps = {
    title: string;
    children?: ReactNode;
    isOpen: boolean;
    onClick: () => void;
    className?: string;
};

const Accordion = ({
    title,
    children,
    isOpen,
    onClick,
    className = "",
}: AccordionProps) => {
    return (
        <div className={`mb-4 text-formText`}>
            <button
                className={`border-borderSecondary ${isOpen ? 'bg-[#E1FFEB]' : 'bg-[#E9FFF0]'} flex w-full cursor-pointer items-center justify-between rounded-lg border p-4`}
                onClick={onClick}
            >
                <span className='text-[16px] font-bold'>{title}</span>
                <span className='transform transition-transform duration-500'>
                    {isOpen ? <Minus /> : <Plus />}
                </span>
            </button>
            <div
                className={`${className} border-borderSecondary ${isOpen ? 'bg-[#E1FFEB]' : 'bg-[#E9FFF0]'}  mt-1.5 rounded-lg border transition-all duration-300 overflow-hidden text-[#01655A] `}
            style={{
                maxHeight: isOpen ? '500px' : '0',
                opacity: isOpen ? 1 : 0,
                visibility: isOpen ? 'visible' : 'hidden',
                padding: isOpen ? '16px' : '0 16px',
                transition: 'max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease, visibility 0.3s',
                fontWeight: isOpen ? 'bolder' : ''
            }}
            aria-hidden={!isOpen}
            >
            {children}
        </div>
        </div >
    );
};

export default Accordion;
// ${isOpen ? 'bg-[#E1FFEB]' : 'bg-[#E9FFF0]'}