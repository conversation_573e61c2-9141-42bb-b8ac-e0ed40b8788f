import { Minus, Plus } from "lucide-react";
import { ReactNode, useState } from "react";
import React from "react";

type AccordionProps = {
    title: string;
    children?: ReactNode;
    isOpen?: boolean;
    className?: string;
};

const Accordion = ({
    title,
    children,
    isOpen: defaultOpen = false,
    className = "",
}: AccordionProps) => {
    const [isOpen, setIsOpen] = useState(defaultOpen);
    const [shouldRender, setShouldRender] = useState(defaultOpen);

    // Handle mounting/unmounting for smooth transition
    React.useEffect(() => {
        if (isOpen) {
            setShouldRender(true);
        } else {
            // Wait for transition to finish before unmounting
            const timeout = setTimeout(() => setShouldRender(false), 300);
            return () => clearTimeout(timeout);
        }
    }, [isOpen]);

    return (
        <div className={`mb-4 text-formText`}>
            <button
                className='border-borderSecondary bg-[#E1FFEB] flex w-full cursor-pointer items-center justify-between rounded-lg border p-4'
                onClick={() => setIsOpen(!isOpen)}>
                <span className='text-[16px] font-medium'>{title}</span>
                <span className='transform transition-transform duration-500'>
                    {isOpen ? <Minus /> : <Plus />}
                </span>
            </button>
            <div
                className={`${className} border-borderSecondary bg-[#E1FFEB] mt-1.5 rounded-lg border transition-all duration-300 overflow-hidden`}
                style={{
                    maxHeight: isOpen ? '500px' : '0',
                    opacity: isOpen ? 1 : 0,
                    visibility: isOpen ? 'visible' : 'hidden',
                    padding: isOpen ? '16px' : '0 16px',
                    transition: 'max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease, visibility 0.3s',
                }}
                aria-hidden={!isOpen}
            >
                {shouldRender && children}
            </div>
        </div>
    );
};

export default Accordion;
