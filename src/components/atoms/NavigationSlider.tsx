import { useState } from 'react';

type NavigationSliderProps = {
    option1: string;
    option2: string;
    onOption1Click?: () => void;
    onOption2Click?: () => void;
    defaultSelected?: 'option1' | 'option2';
}

const NavigationSlider = ({
    onOption1Click,
    onOption2Click,
    option1,
    option2,
    defaultSelected = 'option1'
}: NavigationSliderProps) => {
    const [selectedOption, setSelectedOption] = useState<'option1' | 'option2'>(defaultSelected);

    const handleOption1Click = () => {
        setSelectedOption('option1');
        onOption1Click?.();
    };

    const handleOption2Click = () => {
        setSelectedOption('option2');
        onOption2Click?.();
    };

    const baseClass = 'cursor-pointer flex-1 text-center py-1 px-3 transition-colors duration-200 text-sm md:text-md lg:text-lg font-medium rounded-full';

    const getButtonClass = (isSelected: boolean) =>
        `${baseClass} ${isSelected ? 'bg-background text-primary' : 'text-gray hover:text-primary'}`;

    return (
        <div className="md:mb-8 max-w-70 bg-[#F3F7FB] rounded-[82px] shadow-[0px_1px_4px_0px_rgba(0,0,0,0.12)] px-1 py-1 flex items-center gap-1.5">
            {/* option1 Button */}
            <button
                onClick={handleOption1Click}
                className={getButtonClass(selectedOption === 'option1')}
            >
                {option1}
            </button>

            {/* option2 Button */}
            <button
                onClick={handleOption2Click}
                className={getButtonClass(selectedOption === 'option2')}
            >
                {option2}
            </button>
        </div>
    );
};

export default NavigationSlider