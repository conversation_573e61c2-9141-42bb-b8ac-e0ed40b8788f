import Image from "next/image";

type ZoomButtonProps = {
    onZoomOut?: () => void;
    onZoomIn?: () => void;
    onLocation?:()=> void;
    disableZoomOut?: boolean;
    disableZoomIn?: boolean;
    disableLocation?:boolean;
    className?: string;
}

const ZoomButton = ({
    onZoomOut,
    onZoomIn,
    onLocation,
    disableZoomOut = false,
    disableZoomIn = false,
    disableLocation = false,
    className = '',
}: ZoomButtonProps) => {
    return (
        <div className={`px-2.5 py-5 flex flex-col justify-between items-center gap-4  bg-white/30 rounded-[40px] backdrop-blur-lg ${className}`}>
            {/* location Button */}
            <button
                type="button"
                onClick={onLocation}
                disabled={disableLocation}
                className="cursor-pointer w-12 h-12 bg-primary p-2.5 rounded-full disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center "
            >
                <Image src="/images/location-icon.svg" alt="saveLogo" width={8} height={8} className="w-8 h-8 flex-shrink-0" />

            </button>
            {/* Divider */}
            <div className="w-full h-[1px] bg-[#373737]"></div>

            {/* ZoomIn Button */}
            <button
                type="button"
                onClick={onZoomIn}
                disabled={disableZoomIn}
                className="cursor-pointer w-12 h-12 rounded-full disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center "
            >
                <Image src="/images/zoomInIcon.svg" alt="saveLogo" width={10} height={10} className="w-12 h-12 flex-shrink-0" />

            </button>
            {/* ZoomOut Button */}
            <button
                type="button"
                onClick={onZoomOut}
                disabled={disableZoomOut}
                className="cursor-pointer w-12 h-12 rounded-full disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center "
            >
                <Image src="/images/zoomOutIcon.svg" alt="saveLogo" width={10} height={10} className="w-12 h-12  flex-shrink-0" />

            </button>
        </div>
    )
}

export default ZoomButton