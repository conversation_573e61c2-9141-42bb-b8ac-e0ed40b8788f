import NavigationButtons from '@/components/atoms/NavigationButtons';
import NavigationSlider from '@/components/atoms/NavigationSlider';
import ServiceCard from '@/components/molecules/ServiceCard';
import { useScreenSize } from '@/context/ScreenSizeContext';
import { urbanServicesCommercial, urbanServicesZones } from '@/data/homePage';
import { slugify } from '@/utils/slugify';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { CarouselWrapper } from '../CarouselWrapper';

const UrbanClassification = () => {
    const t = useTranslations('urbanClassification');
    const tCommercial = useTranslations('urbanServicesCommercial');
    const tZones = useTranslations('urbanServicesZones');
    const { isMobile } = useScreenSize();
    const [selectedDataType, setSelectedDataType] = useState<'option1' | 'option2'>('option1');

    const getCurrentData = () => {
        return selectedDataType === 'option1' ? urbanServicesCommercial : urbanServicesZones;
    };

    const currentData = getCurrentData();

    const handleOption1Click = () => {
        setSelectedDataType('option1');
    };

    const handleOption2Click = () => {
        setSelectedDataType('option2');
    };

    return (
        <section
            id="urban-classification"
            className={`text-black px-4 sm:px-6 lg:px-8 ${isMobile ? 'py-10 ' : 'bg-secondary py-30 '} `}
        >
            <div className="mx-auto container">
                <div className="flex flex-col gap-4 md:gap-20">
                    <div className="flex flex-col lg:flex-row md:gap-15">
                        <div className="lg:w-1/2 md:space-y-6">
                            <h2 className="text-zinc-800 text-2xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-10">
                                {t('urbanTitle')}
                            </h2>
                            {/* Render NavigationSlider only in larger screens */}
                            {!isMobile && (
                                <NavigationSlider
                                    option1={t('sliderOption1')}
                                    option2={t('sliderOption2')}
                                    onOption1Click={handleOption1Click}
                                    onOption2Click={handleOption2Click}
                                    defaultSelected={selectedDataType}
                                />
                            )}
                        </div>
                        <div className="lg:w-1/2 space-y-4 md:space-y-6">
                            {/* urbanIntro */}
                            <div className="text-neutral-700 text-xs md:text-md lg:text-lg font-light md:font-normal md:leading-relaxed space-y-4">
                                <p>{t('urbanIntro')}</p>
                            </div>
                            {/* Render NavigationSlider under urbanIntro in mobile view */}
                            {isMobile && (
                                <NavigationSlider
                                    option1={t('sliderOption1')}
                                    option2={t('sliderOption2')}
                                    onOption1Click={handleOption1Click}
                                    onOption2Click={handleOption2Click}
                                    defaultSelected={selectedDataType}
                                />
                            )}
                        </div>
                    </div>

                    {/* Slider */}
                    <CarouselWrapper
                        items={currentData}
                        defaultVisibleCards={isMobile ? 2 : 3}
                        navigationClassName="mt-20"
                        disableNavButtons={false}
                        renderItem={(service) => (
                            <ServiceCard
                                id={service.id}
                                key={service.id}
                                image={service.image}
                                title={`${selectedDataType === 'option1' ? tCommercial(`${service.id}.title`) : tZones(`${service.id}.title`)}`}
                                description={service.description}
                                buttonText={service.buttonText ?? ''}
                                onButtonClick={()=> {}}
                                imageHeight={isMobile ? "sm" : "lg"}
                                contentPadding={isMobile ? 'sm' : 'md'}
                                className='h-fit'
                                link={`/urbanClassification/${selectedDataType === 'option1'
                                    ? 'commercial-' + slugify(service.title)
                                    : 'zones-' + slugify(service.title)
                                    }`}
                            />
                        )}
                    />
                </div>
            </div>
        </section>
    );
};

export default UrbanClassification;
