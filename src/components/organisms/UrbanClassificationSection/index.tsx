import NavigationButtons from '@/components/atoms/NavigationButtons';
import NavigationSlider from '@/components/atoms/NavigationSlider';
import ServiceCard from '@/components/molecules/ServiceCard';
import { urbanServicesCommercial, urbanServicesZones } from '@/data/homePage';
import { slugify } from '@/utils/slugify';
import { useTranslations } from 'next-intl';
import { useState, useEffect, useRef, useCallback } from 'react';

const UrbanClassification = () => {
    const t = useTranslations('urbanClassification');
    const tCommercial = useTranslations('urbanServicesCommercial');
    const tZones = useTranslations('urbanServicesZones');

    const sliderContentRef = useRef<HTMLDivElement>(null);

    const [isReady, setIsReady] = useState(false);
    const [cardWidth, setCardWidth] = useState(0);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [selectedDataType, setSelectedDataType] = useState<'option1' | 'option2'>('option1');
    const [visibleCards, setVisibleCards] = useState(4);
    const [isHovered, setIsHovered] = useState(false);

    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const getCurrentData = () => {
        return selectedDataType === 'option1' ? urbanServicesCommercial : urbanServicesZones;
    };

    const currentData = getCurrentData();
    const tripleProducts = [...currentData, ...currentData, ...currentData];

    const getVisibleCardsCount = useCallback(() => {
        if (typeof window === 'undefined') return 4;

        const width = window.innerWidth;
        if (width >= 1024) return 4;
        if (width >= 768) return 2;
        return 1;
    }, []);

    const calculateDimensions = useCallback(() => {
        const content = sliderContentRef.current;
        if (!content) return;

        const container = content.parentElement;
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const containerW = containerRect.width;

        const cardsToShow = getVisibleCardsCount();
        setVisibleCards(cardsToShow);

        const gap = cardsToShow === 1 ? 0 : 16;
        const actualCardWidth =
            cardsToShow === 1
                ? containerW
                : (containerW - gap * (cardsToShow - 1)) / cardsToShow;

        setCardWidth(actualCardWidth);
        setIsReady(true);

        const initialIndex = currentData.length;
        setCurrentIndex(initialIndex);

        requestAnimationFrame(() => {
            const initialPosition = initialIndex * (actualCardWidth + gap);
            content.style.transform = `translateX(${initialPosition}px)`;
        });
    }, [currentData, getVisibleCardsCount]);

    useEffect(() => {
        calculateDimensions();
        window.addEventListener('resize', calculateDimensions);
        return () => window.removeEventListener('resize', calculateDimensions);
    }, [calculateDimensions]);

    useEffect(() => {
        setIsReady(false);
        setCurrentIndex(0);
        calculateDimensions();
    }, [selectedDataType, calculateDimensions]);

    const shouldDisableNavigation = currentData.length <= visibleCards;

    // Auto slide
    useEffect(() => {
        if (!isReady || shouldDisableNavigation) return;

        const startAutoSlide = () => {
            intervalRef.current = setInterval(() => {
                if (!isHovered) {
                    handleNext();
                }
            }, 5000); // every 5 seconds
        };

        startAutoSlide();

        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [isReady, isHovered, cardWidth, currentIndex]);

    // Navigation functions
    const handleNext = () => {
        const content = sliderContentRef.current;
        if (!content || !isReady || shouldDisableNavigation) return;

        const gap = visibleCards === 1 ? 0 : 16;
        let newIndex = currentIndex + 1;

        if (newIndex >= currentData.length * 2) {
            newIndex = currentData.length;
        }

        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    };

    const handlePrevious = () => {
        const content = sliderContentRef.current;
        if (!content || !isReady || shouldDisableNavigation) return;

        const gap = visibleCards === 1 ? 0 : 16;
        let newIndex = currentIndex - 1;

        if (newIndex < currentData.length) {
            newIndex = currentData.length * 2 - 1;
        }

        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    };

    const goToSlide = (index: number) => {
        const content = sliderContentRef.current;
        if (!content || !isReady || shouldDisableNavigation) return;

        const gap = visibleCards === 1 ? 0 : 16;
        const newIndex = currentData.length + index;
        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    };

    const getCurrentSlideIndex = () => {
        return (currentIndex - currentData.length + currentData.length) % currentData.length;
    };

    const handelUrbanServiceClicked = (serviceId: number) => {
        //console.log(`service with ID:${serviceId} clicked`);
    };

    const handleOption1Click = () => {
        setSelectedDataType('option1');
    };

    const handleOption2Click = () => {
        setSelectedDataType('option2');
    };

    return (
        <section
            id="urban-classification"
            className="text-black px-4 sm:px-6 lg:px-8 py-30 bg-secondary"
        >
            <div className="mx-auto container">
                <div className="flex flex-col gap-20">
                    <div className="flex flex-col lg:flex-row gap-15">
                        <div className="lg:w-1/2 space-y-6">
                            <h2 className="text-zinc-800 text-3xl md:text-4xl lg:text-5xl font-bold mb-10">
                                {t('urbanTitle')}
                            </h2>
                            <NavigationSlider
                                option1={t('sliderOption1')}
                                option2={t('sliderOption2')}
                                onOption1Click={handleOption1Click}
                                onOption2Click={handleOption2Click}
                                defaultSelected={selectedDataType}
                            />
                        </div>
                        <div className="lg:w-1/2 space-y-6">
                            <div className="text-neutral-700 text-sm md:text-md lg:text-lg font-normal leading-relaxed space-y-4">
                                <p>{t('urbanIntro')}</p>
                            </div>
                        </div>
                    </div>

                    {/* Slider */}
                    <div
                        className="relative overflow-hidden"
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                    >
                        <div
                            className={`flex w-max ${visibleCards === 1 ? 'gap-0' : 'gap-4'}`}
                            ref={sliderContentRef}
                            style={{
                                opacity: isReady ? 1 : 0,
                                transition: isReady ? undefined : 'opacity 0.3s ease-in-out',
                            }}
                        >
                            {tripleProducts.map((service, index) => (
                                <div
                                    key={`${service.id}-${index}-${selectedDataType}`}
                                    className="flex-shrink-0"
                                    style={{ width: `${cardWidth}px` }}
                                >
                                    <ServiceCard
                                        id={service.id}
                                        key={service.id}
                                        image={service.image}
                                        title={`${selectedDataType === 'option1' ? tCommercial(`${service.id}.title`) : tZones(`${service.id}.title`)}`}
                                        description={service.description}
                                        buttonText={service.buttonText ?? ''}
                                        onButtonClick={() => handelUrbanServiceClicked(service.id)}
                                        imageHeight="lg"
                                        className='h-fit'
                                        link={`/urbanClassification/${selectedDataType === 'option1'
                                            ? 'commercial-' + slugify(service.title)
                                            : 'zones-' + slugify(service.title)
                                            }`}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Navigation */}
                    {!shouldDisableNavigation && (
                        <div>
                            <NavigationButtons
                                className="mt-20"
                                onNext={handlePrevious} //the functionality are switched because of the rtl direction of the app
                                onPrevious={handleNext}
                                disableNext={false}
                                disablePrevious={false}
                            />
                            <div className="flex justify-end space-x-[8px] -mt-8">
                                {currentData.map((_, index) => (
                                    <button
                                        key={index}
                                        onClick={() => goToSlide(index)}
                                        className={`w-2 h-2 rounded-full transition-colors duration-200 cursor-pointer ${index === getCurrentSlideIndex()
                                            ? 'bg-primary'
                                            : 'bg-gray-300'
                                            }`}
                                        aria-label={`Go to slide ${index + 1}`}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};

export default UrbanClassification;
