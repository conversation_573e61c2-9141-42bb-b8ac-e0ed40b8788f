import React from 'react';
import { X } from 'lucide-react';
import { MobileMenuProps } from '@/lib/types';
import Image from 'next/image';
import NavItem from '@/components/molecules/NavItem';
import Link from 'next/link';

const MobileMenu = ({
    isOpen,
    onToggle,
    items,
    activeSection,
    onNavigate,
    disabled = false,
}: MobileMenuProps) => {

    const handleNavigate = (sectionId: string) => {
        if (disabled) return;
        onNavigate(sectionId);
        onToggle();
    };

    return (
        <>
            {/* Mobile Menu */}
            <div
                className={`
                    fixed top-0 right-0 h-full w-full bg-primary shadow-lg transform transition-transform duration-300 ease-in-out z-50 show-at-800 hidden
                    ${isOpen ? 'translate-x-0' : 'translate-x-full'}
                `}
            >
                <div className="flex items-center justify-between p-4 ">
                    <Link href={'/'}>
                        <button onClick={() => onToggle()}>
                            <Image src='/images/madinaMapLogo.svg' width={30} height={30} alt={'madina Map Logo'}
                                className="w-44 cursor-pointer"
                                priority />
                        </button>
                    </Link>
                    <button
                        onClick={onToggle}
                        className="p-2 rounded-md cursor-pointer"
                    >
                        <X className="h-8 w-8 text-white" />
                    </button>
                </div>

                <nav className="py-13 flex flex-col gap-6 mx-auto items-center">
                    {items.map((item) => (
                        <NavItem
                            key={item.id}
                            id={item.id}
                            label={item.label}
                            onClick={() => handleNavigate(item.id)}
                            className={`${!disabled && item.id === activeSection ? '!bg-navHover !border-navHover ' : ''}
                            ${disabled ? 'opacity-0 cursor-not-allowed pointer-events-none' : ''}`}
                        />
                    ))}
                </nav>
            </div>
        </>
    );
};

export default MobileMenu;
