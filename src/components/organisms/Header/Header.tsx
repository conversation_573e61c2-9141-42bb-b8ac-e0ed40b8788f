'use client'

import React, { useState } from 'react';
import { Menu } from 'lucide-react';
import Navigation from './Navigation';
import MobileMenu from './MobileMenu';
import { navigationItems } from '@/data/homePage';
import { useActiveSection } from '@/hooks/useActiveSection';
import { useScrollTo } from '@/hooks/useScrollTo';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';

const Header = () => {
    const pathname = usePathname();
    const isUrbanClassification = pathname.startsWith('/urbanClassification');
    const isMap = pathname.startsWith('/map');
    const activeSection = useActiveSection(navigationItems.map((navItem) => navItem.id));
    const { scrollToSection } = useScrollTo();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const handleNavigation = (sectionId: string) => {
        if (isUrbanClassification || isMap) return;
        scrollToSection(sectionId);
    };

    return (
        <>
            <header className={`w-full z-30  ${isMap ? 'absolute top-0' : ''} ${isUrbanClassification || isMap? '' : 'fixed top-0 left-0 right-0 bg-gradient-to-r from-[var(--gradient-primary-start)] to-[var(--gradient-primary-end)]'}`}>
                <div className="mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-20">
                        {/* Logo */}
                        <div className="flex-shrink-0">
                            <Link href={'/'}>
                                <Image src={isUrbanClassification || isMap ? '/images/madinaMapLogo2.svg' : '/images/madinaMapLogo.svg'} width={30} height={30} alt={'madina Map Logo'}
                                    className="w-44 cursor-pointer"
                                    priority />
                            </Link>
                        </div>

                        {/* Desktop Navigation */}
                        <Navigation
                            items={navigationItems}
                            activeSection={activeSection}
                            onNavigate={handleNavigation}
                            className='flex flex-nowrap'
                            disabled={isUrbanClassification || isMap}
                        />

                        {/* Mobile Menu Button */}
                        <button
                            onClick={toggleMobileMenu}
                            className="cursor-pointer show-at-800 hidden p-2 "
                        >
                            <Menu className={`h-8 w-8 ${isUrbanClassification ? 'text-primary' : 'text-white'}`} />
                        </button>
                    </div>
                </div>
            </header>

            {/* Mobile Menu */}
            <MobileMenu
                isOpen={isMobileMenuOpen}
                onToggle={toggleMobileMenu}
                items={navigationItems}
                activeSection={activeSection}
                onNavigate={handleNavigation}
                disabled={isUrbanClassification}
            />
        </>
    );
};

export default Header;
