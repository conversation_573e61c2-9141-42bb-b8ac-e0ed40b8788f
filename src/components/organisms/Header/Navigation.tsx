import NavItem from '@/components/molecules/NavItem';
import { NavigationProps } from '@/lib/types';
import React from 'react';

const Navigation = ({
    items,
    activeSection,
    onNavigate,
    className = '',
    disabled = false,
}: NavigationProps) => {

    return (
        <nav className={`hide-at-800 md:space-x-5 ${className}`}>
            {items.map((item) => (
                <NavItem
                    key={item.id}
                    id={item.id}
                    label={item.label}
                    onClick={() => onNavigate(item.id)}
                    className={`${!disabled && item.id === activeSection ? '!bg-navHover !border-navHover ' : ''}
                    ${disabled ? 'opacity-0 cursor-not-allowed pointer-events-none' : ''}`}
                />
            ))}
        </nav>
    );
};

export default Navigation;
