import { Button } from "@/components/atoms/Button"
import { useScreenSize } from "@/context/ScreenSizeContext";
import { useTranslations } from "next-intl"
import { useEffect, useState } from "react";


const Hero = () => {
  const t = useTranslations('hero');
  const tCommon = useTranslations('Common')
  const { isMobile } = useScreenSize(); // Use the screen size context
  const backgroundImage = isMobile ? "/images/heroBackgroundTest1.png" : "/images/heroBackground.svg"


  return (
    <section id="hero" className="relative bg-gradient-to-r from-[var(--gradient-primary-start)] to-[var(--gradient-primary-end)] h-screen max-h-[707px] md:max-h-screen overflow-hidden">
      {/* Background image */}
      <div
        className={`w-full h-full hero-background`}
        style={{
          backgroundImage: `url(${backgroundImage})`,
        }}
      ></div>
      <div className="w-full overflow-hidden flex flex-col  mt-[20%] md:mt-[10%] absolute left-0 right-0 top-0  text-white px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto ">
          <div className="md:w-[60%] w-ful">
            <h1 className="font-bold text-2xl md:text-4xl lg:text-5xl lg:leading-[57.60px] mb-10">
              {t('title1')}
            </h1>
            <p className="text-sm md:text-md lg:text-lg font-medium leading-relaxed">
              {t('intro')}
            </p>
          </div>
          <div className="my-10">
            <a href="/map">
              <Button
                variant="gradient"
                hasArrow
                className="!py-2">
                {tCommon('getService')}
              </Button>
            </a>
          </div>
        </div>

      </div>
    </section>
  )
}

export default Hero
