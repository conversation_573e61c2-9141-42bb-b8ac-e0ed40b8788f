import { Button } from "@/components/atoms/Button"
import { useTranslations } from "next-intl"


const Hero = () => {
  const t = useTranslations('hero');
  const tCommon = useTranslations('Common')

  return (
    <section id="hero" className="relative bg-gradient-to-r from-[var(--gradient-primary-start)] to-[var(--gradient-primary-end)]">
      {/* Background image */}
      <div
        className={`w-full h-screen`}
        style={{
          backgroundImage: `url(/images/heroBackground.svg)`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'bottom',
        }}
      ></div>
      <div className="w-full overflow-hidden flex flex-col  mt-[20%] md:mt-[10%] absolute left-0 right-0 top-0  text-white px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto ">
          <div className="md:w-[60%] w-ful">
            <h1 className="font-bold text-3xl md:text-4xl lg:text-5xl leading-[57.60px] mb-10">
              {t('title1')}
            </h1>
            <p className="text-sm md:text-md lg:text-lg font-medium leading-relaxed">
              {t('intro')}
            </p>
          </div>
          <div className="my-10">
            <a href="/map">
              <Button
                variant="gradient"
                hasArrow
                className="!py-2">
                {tCommon('getService')}
              </Button>
            </a>
          </div>
        </div>

      </div>
    </section>
  )
}

export default Hero
