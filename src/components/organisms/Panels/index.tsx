import PanelsCard from "@/components/molecules/PanelsCard";
import { PanelsData } from "@/data/homePage";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";

const Panels = () => {
    const [activeIndex, setActiveIndex] = useState(PanelsData[0].id);
    const t = useTranslations("panels")
    const handleMouseEnter = useCallback((id: number) => {
        setActiveIndex(id);
    }, []);


    return (
        <section id="panels" className="text-white px-4 sm:px-6 lg:px-8 py-6 md:py-30 bg-[#007366] md:bg-secBackground">
            <div className="mx-auto container">
                {/* Slider Container */}
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-5 md:mb-20">
                    {t("title")}
                </h2>

                <div className="flex flex-col lg:flex-row w-full overflow-hidden">
                    {PanelsData.map((panel, index) => {
                        const isExpanded = activeIndex === panel.id;

                        return (
                            <div
                                key={panel.id}
                                className={`transition-all duration-500 ease-in-out cursor-pointer
                                    ${isExpanded
                                        ? 'w-full min-h-[450px] pb-10 md:pb-0 lg:py-0 lg:w-[90%] lg:min-h-[850px] bg-[#F1F7F3]'
                                        : 'bg-white w-full min-h-[50px] lg:w-[10%] lg:min-h-[850px]'
                                    }
                                     md:bg-[#F1F7F3] border border-[#D3D3D3] flex flex-col items-center justify-center`}
                                onClick={() => handleMouseEnter(panel.id)}
                                tabIndex={index}
                                role="button"
                                aria-expanded={isExpanded}
                                aria-label={`${panel.title} project. ${isExpanded ? 'Currently expanded' : 'Click to expand'}`}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        handleMouseEnter(panel.id);
                                    }
                                }}
                            >
                                {isExpanded ? (
                                    <PanelsCard
                                        key={panel.id}
                                        {...panel}
                                    />
                                ) : (
                                    // Section for non expand case
                                    <div className="flex flex-row items-center justify-center h-[50px] container relative w-full lg:justify-between lg:flex-col lg:min-h-[850px] lg:mt-8">
                                        {/* Card Number */}
                                        <div className="pr-0 p-2 lg:p-3 lg:flex-1">
                                            <h4 className="text-2xl font-bold text-black">
                                                {panel.cardNumber}
                                            </h4>
                                        </div>
                                        {/* Card Title */}
                                        <div className="flex-1 flex items-center justify-start lg:absolute lg:bottom-0 lg:left-0 lg:p-12">
                                            <div className="whitespace-nowrap lg:transform lg:-rotate-90 lg:origin-bottom-left">
                                                <h3 className="text-2xl font-bold text-black tracking-wide">
                                                    {panel.title}
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )
                    })}
                </div>
            </div>
        </section>
    )
}

export default Panels