import React, { ReactNode } from 'react';
import NavigationButtons from '@/components/atoms/NavigationButtons';
import { useCarousel } from '@/hooks/useCarousel';

interface CarouselWrapperProps<T> {
    items: T[];
    renderItem: (item: T, index: number, cardWidth: number) => ReactNode;
    autoSlide?: boolean;
    autoSlideInterval?: number;
    getVisibleCardsCount?: () => number;
    defaultVisibleCards?: number;
    showNavigation?: boolean;
    showDots?: boolean;
    disableNavButtons?: boolean;
    navigationClassName?: string;
    containerClassName?: string;
}

export function CarouselWrapper<T extends { id: string | number }>({
    items,
    renderItem,
    autoSlide = true, // Default to true
    autoSlideInterval = 5000,
    getVisibleCardsCount,
    defaultVisibleCards = 1,
    showNavigation = true,
    showDots = true,
    disableNavButtons = false,
    navigationClassName = '',
    containerClassName = '',
}: CarouselWrapperProps<T>) {
    const {
        sliderContentRef,
        isReady,
        cardWidth,
        visibleCards,
        tripleItems,
        handleNext,
        handlePrevious,
        goToSlide,
        getCurrentSlideIndex,
        onTouchStart,
        onTouchMove,
        onTouchEnd,
        setIsHovered,
    } = useCarousel({
        items,
        autoSlideInterval: autoSlide ? autoSlideInterval : 0,
        getVisibleCardsCount,
        defaultVisibleCards,
    });

    return (
        <>
            {/* Slider */}
            <div
                className={`relative overflow-hidden ${containerClassName}`}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}
            >
                <div
                    className={`flex w-max ${visibleCards === 1 ? 'gap-0' : 'gap-4'}`}
                    ref={sliderContentRef}
                    style={{
                        opacity: isReady ? 1 : 0,
                        transition: isReady ? undefined : 'opacity 0.3s ease-in-out',
                    }}
                >
                    {tripleItems.map((item, index) => (
                        <div
                            key={`${item.id}-${index}`}
                            className="flex-shrink-0"
                            style={{ width: `${cardWidth}px` }}
                        >
                            {renderItem(item, index, cardWidth)}
                        </div>
                    ))}
                </div>
            </div>

            {/* Navigation */}
            {showNavigation && (
                <div>
                    <NavigationButtons
                        className={navigationClassName}
                        onNext={handleNext}
                        onPrevious={handlePrevious}
                        disableNext={disableNavButtons}
                        disablePrevious={disableNavButtons}
                    />
                    {showDots && (
                        <div className="flex justify-end space-x-[8px] -mt-8">
                            {items.map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        if (!disableNavButtons) goToSlide(index);
                                    }}
                                    className={`w-2 h-2 rounded-full transition-colors duration-200 ${disableNavButtons
                                        ? 'cursor-not-allowed opacity-50'
                                        : 'cursor-pointer'
                                        } ${index === getCurrentSlideIndex() ? 'bg-primary' : 'bg-gray-300'
                                        }`}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    )}
                </div>
            )}
        </>
    );
}