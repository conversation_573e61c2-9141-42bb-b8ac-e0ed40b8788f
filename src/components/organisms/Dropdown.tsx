"use client"
import Dropdown from "../atoms/Dropdown";
import { DropdownOption } from "@/lib/types";

type DropdownSectionProps = {
    municipality: string;
    setMunicipality: (value: string) => void;
    municipalityOptions: DropdownOption[];
    planName: string;
    setPlanName: (value: string) => void;
    planNameOptions: DropdownOption[];
    planNo: string;
    setPlanNo: (value: string) => void;
    planNoOptions: DropdownOption[];
    parcelNo: string;
    setParcelNo: (value: string) => void;
    parcelNoOptions: DropdownOption[];
};

const DropdownSection = ({
    municipality,
    setMunicipality,
    municipalityOptions,
    planName,
    setPlanName,
    planNameOptions,
    planNo,
    setPlanNo,
    planNoOptions,
    parcelNo,
    setParcelNo,
    parcelNoOptions,
}: DropdownSectionProps) => {
    return (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-3 ">
            <Dropdown
                label="البلدية"
                placeholder="--- أختر البلدية ---"
                options={municipalityOptions}
                value={municipality}
                onChange={setMunicipality}
                searchable={true}
            />
            <Dropdown
                label="الحي"
                placeholder="--- أختر الحي ---"
                options={planNameOptions}
                value={planName}
                onChange={setPlanName}
                searchable={true}
            />
            <Dropdown
                label="رقم المخطط"
                placeholder="--- أختر رقم المخطط ---"
                options={planNoOptions}
                value={planNo}
                onChange={setPlanNo}
                searchable={true}
            />
            <Dropdown
                label="رقم القطعة"
                placeholder="--- أختر رقم القطعة ---"
                options={parcelNoOptions}
                value={parcelNo}
                onChange={setParcelNo}
                searchable={true}
            />
        </div>
    );
};

export default DropdownSection;
