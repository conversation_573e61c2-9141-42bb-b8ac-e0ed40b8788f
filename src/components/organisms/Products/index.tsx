import ProductAndLightCard from "@/components/molecules/Product&LightCard"
import { ProductsData } from "@/data/homePage"
import { useTranslations } from "next-intl"
import { useScreenSize } from "@/context/ScreenSizeContext";
import ResponsiveCarouselGrid from "@/components/molecules/ProductCarousel";

const Products = () => {
    const t = useTranslations("Common")
    const { isMobile } = useScreenSize();

    return (
        <section id="products" className="text-black md:bg-thrBackground px-4 sm:px-6 lg:px-8 pt-6 md:py-30"
            style={{
                backgroundImage: isMobile ? '' : 'url(/images/LightingBackground.svg)',
                backgroundSize: '25% auto',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'top left',
            }}
        >
            {/* Main Content */}
            <div className="mx-auto container flex flex-col">
                <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold md:mb-20">{t("products")}</h2>

                <ResponsiveCarouselGrid
                    data={ProductsData}
                    renderCard={(card) => (
                        <ProductAndLightCard
                            key={card.id}
                            id={card.id}
                            image={card.image}
                            title={card.title}
                            imageHeight="sm"
                        />
                    )}
                />
            </div>
        </section>
    )
}

export default Products