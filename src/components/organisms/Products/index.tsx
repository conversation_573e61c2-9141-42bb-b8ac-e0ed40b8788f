// import { useTranslations } from "next-intl"
import ProductAndLightCard from "@/components/molecules/Product&LightCard"
import { ProductsData } from "@/data/homePage"
import { useTranslations } from "next-intl"

const Products = () => {

    const t = useTranslations("Common")
    return (
        <section id="products" className="text-black bg-thrBackground px-4 sm:px-6 lg:px-8 py-30"
            style={{
                backgroundImage: `url(/images/LightingBackground.svg)`,
                backgroundSize: '25% auto',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'top left',
            }}
        >
            {/* Main Content */}
            <div className="mx-auto container flex flex-col">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-20">{t("products")}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                    {ProductsData.map((card) => (
                        <ProductAndLightCard
                            id={card.id}
                            key={card.id}
                            image={card.image}
                            title={card.title}
                            imageHeight="lg"
                        />
                    ))}
                </div>
            </div>
        </section>
    )
}

export default Products