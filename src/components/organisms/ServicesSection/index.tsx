import ServiceCard from "@/components/molecules/ServiceCard";
import { useScreenSize } from "@/context/ScreenSizeContext";
import { services } from "@/data/homePage";
import { CarouselWrapper } from "@/components/organisms/CarouselWrapper";
import { useTranslations } from "next-intl";

const Services = () => {
    const t = useTranslations("services");
    const { isMobile } = useScreenSize();

    return (
        <section id="services" className="text-black px-4 sm:px-6 lg:px-20 bg-white md:bg-secondary">
            <div className="bg-white md:bg-[#F1F7F3] pt-7 md:pt-20">
                <div className="mx-auto container md:pb-10">
                    <div className="flex flex-col gap-6">
                        {/* Header Section */}
                        <div className="flex flex-col lg:flex-row">
                            <div className="lg:w-1/2 space-y-6 gap-3 -mb-4 md:mb-0">
                                <h2 className="text-zinc-800 text-2xl md:text-4xl lg:text-5xl font-bold mb-10">
                                    {t("servicesTitle")}
                                </h2>
                            </div>
                            <div className="lg:w-1/2 space-y-6">
                                <div className="text-neutral-700 text-sm md:text-md lg:text-lg font-normal leading-relaxed space-y-4">
                                    <p>{t("servicesIntro")}</p>
                                </div>
                            </div>
                        </div>

                        {/* Services Display */}
                        {isMobile ? (
                            <CarouselWrapper
                                items={services}
                                defaultVisibleCards={2}
                                navigationClassName="mt-20"
                                disableNavButtons={false}
                                renderItem={(service) => (
                                    <ServiceCard
                                        id={service.id}
                                        key={service.id}
                                        image={service.image}
                                        title={service.title}
                                        description={service.description}
                                        imageHeight="sm"
                                        contentPadding="sm"
                                        link={service.link}
                                    />
                                )}
                            />
                        ) : (
                            <CarouselWrapper
                                items={services}
                                defaultVisibleCards={3}
                                navigationClassName="mt-20"
                                disableNavButtons={true}
                                autoSlide={isMobile}
                                renderItem={(service) => (
                                    <ServiceCard
                                        id={service.id}
                                        key={service.id}
                                        image={service.image}
                                        title={service.title}
                                        description={service.description}
                                        imageHeight="sm"
                                        contentPadding="sm"
                                        link={service.link}
                                    />
                                )}
                            />
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Services;