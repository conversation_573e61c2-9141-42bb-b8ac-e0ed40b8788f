import NavigationButtons from "@/components/atoms/NavigationButtons";
import ServiceCard from "@/components/molecules/ServiceCard";
import { services } from "@/data/homePage";
import { useTranslations } from "next-intl";


const Services = () => {

    const t = useTranslations("services");

    return (
        <section id="services" className="text-black px-4 sm:px-6 lg:px-20 pb-30 bg-secondary">
            <div className="bg-[#F1F7F3]  pt-44">
                <div className="mx-auto container pb-10">
                    {/* Main Content */}
                    <div className="flex flex-col gap-20">
                        <div className="flex flex-col lg:flex-row">
                            {/* Text Content */}
                            <div className="lg:w-1/2 space-y-6 gap-3">
                                <h2 className="text-zinc-800 text-3xl md:text-4xl lg:text-5xl font-bold mb-10">
                                    {t("servicesTitle")}
                                </h2>
                            </div>
                            <div className="lg:w-1/2 space-y-6">
                                <div className="text-neutral-700 text-sm md:text-md lg:text-lg font-normal leading-relaxed space-y-4">
                                    <p> {t("servicesIntro")}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Services Grid - Static display of all 3 cards */}
                        <div className="w-full">
                            {/* Cards Container - Simple grid layout */}
                            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-8 mx-auto">
                                {services.map((service) => (
                                    <ServiceCard
                                        id={service.id}
                                        key={service.id}
                                        image={service.image}
                                        title={service.title}
                                        description={service.description}
                                        imageHeight="lg"
                                        link={service.link}
                                    />
                                ))}
                            </div>
                            {/* Navigation Buttons */}
                            <NavigationButtons className="mt-20" disableNext disablePrevious />
                            {/* Pagination Dots */}
                            <div className="flex justify-end space-x-[8px] -mt-8">
                                {services.map((_, index) => (
                                    <button
                                        key={index}
                                        className={`w-2 h-2 rounded-full transition-colors duration-200 cursor-pointer bg-primary `}
                                        aria-label={`Go to slide ${index + 1}`}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Services