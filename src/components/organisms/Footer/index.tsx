'use client'

import { Button } from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface FormData {
    name: string;
    phone: string;
    email: string;
    message: string;
}

interface FormErrors {
    name?: string;
    phone?: string;
    email?: string;
    message?: string;
}

const Footer = () => {
    const toEmail = '<EMAIL>'
    const t = useTranslations("Common");
    const tFooter = useTranslations("Footer");
    // Form state
    const [formData, setFormData] = useState<FormData>({
        name: '',
        phone: '',
        email: '',
        message: ''
    });

    const [error, setError] = useState(false);
    const [errors, setErrors] = useState<FormErrors>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const linksItems = [
        { href: 'hero', label: 'hero' },
        { href: 'services', label: 'services' },
        { href: 'products', label: 'products' },
        { href: 'urban-classification', label: 'urban-classification' },
        { href: 'panels', label: 'panels' },
        { href: 'lighting', label: 'lighting' },
    ];

    const iconsItems = [
        { imgSource: 'Youtube', href: 'https://www.youtube.com/@AmanaAlmadinah' },
        { imgSource: 'LinkedIn', href: 'https://www.linkedin.com/company/amanaalmadinah/?originalSubdomain=sa' },
        { imgSource: 'X', href: 'https://x.com/AmanaAlmadinah' },
        { imgSource: 'Instagram', href: 'https://www.instagram.com/amana.almadinah/' },
        { imgSource: 'Facebook', href: 'https://www.facebook.com/AmanaAlmadinah/' },
    ];

    // Validation functions
    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const validatePhone = (phone: string): boolean => {
        // Saudi phone number validation (supports +966, 966, or starts with 05)
        const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    };


    const validateForm = (): boolean => {
        setErrors({});
        let isValid = true;

        // Name validation
        if (!formData.name.trim() || formData.name.trim().length < 2) {
            setErrors(prev => ({ ...prev, name: tFooter("name-error") }));
            isValid = false;
        }

        // Phone validation
        if (!formData.phone.trim() || !validatePhone(formData.phone)) {
            setErrors(prev => ({ ...prev, phone: tFooter("phone-error") }));
            isValid = false;
        }

        // Email validation
        if (!formData.email.trim() || !validateEmail(formData.email)) {
            setErrors(prev => ({ ...prev, email: tFooter("email-error") }));
            isValid = false;
        }

        // Message validation
        if (!formData.message.trim() || formData.message.trim().length < 10) {
            setErrors(prev => ({ ...prev, message: tFooter("message-error") }));
            isValid = false;
        }

        setError(!isValid);
        if (!isValid) setSubmitStatus('error');

        return isValid;
    };


    // Handle input changes
    const handleInputChange = (field: keyof FormData) => (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const value = e.target.value;
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error when user starts typing
        if (error) {
            setError(false);
        }

        // Reset submit status
        if (submitStatus !== 'idle') {
            setSubmitStatus('idle');
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsSubmitting(true);

        try {
            // Simulate API call - replace with the actual API endpoint and deal with the formData
            // Format the email body with form data
            const emailBody = `
            Name: ${formData.name}\n Email: ${formData.email}\n Phone: ${formData.phone}\n  Message:${formData.message}`.trim();

            const encodedBody = encodeURIComponent(emailBody);
            const encodedSubject = encodeURIComponent("Contact Form Submission");
            // Redirect to email client
            window.location.href = `mailto:${toEmail}?subject=${encodedSubject}&body=${encodedBody}`;
            setFormData({
                name: '',
                phone: '',
                email: '',
                message: ''
            })
            setSubmitStatus('idle');
            setErrors({});
            // console.log(JSON.stringify(formData))
            setSubmitStatus('success');
        } catch (error) {
            console.error('Form submission error:', error);
            setSubmitStatus('error');
        } finally {
            setIsSubmitting(false);
        }
    };

    useEffect(() => {
        if (submitStatus === 'success' || submitStatus === 'error') {
            const timeout = setTimeout(() => {
                setSubmitStatus('idle');
            }, 10000); // 10 seconds

            return () => clearTimeout(timeout);
        }
    }, [submitStatus]);

    return (
        <div id="contact" className="text-white bg-primary px-4 sm:px-6 lg:px-8 pt-50 py-10 min-h-screen">
            <div className="mx-auto container flex flex-col lg:flex-row items-center gap-5 p-12 rounded-2xl bg-gradient-to-r from-[var(--gradient-secondary-start)] to-[var(--gradient-secondary-end)]">
                {/* Contact Links */}
                <div className='lg:w-1/2 py-10'>
                    <Image
                        src='/images/madinaMapLogo.svg'
                        width={30}
                        height={30}
                        alt={'madina Map Logo'}
                        className="w-80 h-20 cursor-pointer -ms-5"
                        priority
                    />
                    <div className='py-10'>
                        <h3 className='mb-4 text-base md:text-xl lg:text-2xl'>{t("links")}</h3>
                        <div className='flex flex-row flex-wrap text-sm lg:text-base font-medium gap-5'>
                            {linksItems.map((item) => (
                                <Link key={item.label} href={`#${item.href}`}>
                                    <span>{t(item.label)}</span>
                                </Link>
                            ))}
                        </div>
                    </div>
                    <div className='py-10'>
                        <h3 className='text-base md:text-xl lg:text-2xl mb-4'>{t("contact-us")}</h3>
                        <div className='flex flex-row text-sm gap-5'>
                            {iconsItems.map((item) => (
                                <Link href={item.href} key={item.imgSource} target='_blank'>
                                    <Image
                                        src={`/images/${item.imgSource}.svg`}
                                        width={20}
                                        height={20}
                                        alt={`${item.imgSource} Logo`}
                                        className="w-6 h-6 cursor-pointer"
                                        priority
                                    />
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Contact Form */}
                <div className='lg:w-1/2'>
                    <form onSubmit={handleSubmit} className='w-full bg-[#a4c6c2] rounded-2xl p-5'>
                        <Input
                            label={tFooter("name")}
                            placeholder={tFooter("full-name")}
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange('name')}
                            fullWidth={true}
                            error={error}
                        />
                        <Input
                            label={tFooter("phone")}
                            placeholder={tFooter("type-here")}
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange('phone')}
                            error={error}
                        />
                        <Input
                            label={tFooter("email")}
                            placeholder={tFooter("email-placeholder")}
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange('email')}
                            error={error}
                        />
                        <Input
                            label=''
                            placeholder={tFooter("message-placeholder")}
                            type="textarea"
                            name="message"
                            value={formData.message}
                            onChange={handleInputChange('message')}
                            rows={5}
                            fullWidth={true}
                            error={error}
                        />

                        {/* Submit Status Messages */}
                        {submitStatus === 'success' && (
                            <div className="mb-4 p-3 bg-secondary border border-primary text-primary rounded-lg">
                                {tFooter("success")}
                            </div>
                        )}

                        {submitStatus === 'error' && Object.keys(errors).length > 0 && (
                            <div className="mb-4 p-3 bg-red-100 border border-textError text-textError rounded-lg">
                                <div className="font-medium mb-2">{tFooter("error-message-state")}</div>
                                <ul className="list-disc list-inside space-y-1">
                                    {errors.name && <li>{errors.name}</li>}
                                    {errors.phone && <li>{errors.phone}</li>}
                                    {errors.email && <li>{errors.email}</li>}
                                    {errors.message && <li>{errors.message}</li>}
                                </ul>
                            </div>
                        )}

                        {submitStatus === 'error' && Object.keys(errors).length === 0 && (
                            <div className="mb-4 p-3 bg-red-100 border border-textError text-textError rounded-lg">
                                {tFooter("error-form-state")}
                            </div>
                        )}

                        <Button
                            type="submit"
                            variant="gradient"
                            hasArrow
                            disabled={isSubmitting}
                            className="!py-2 w-full disabled:opacity-50 disabled:cursor-not-allowed">
                            {isSubmitting ? tFooter("sending") : tFooter("send")}
                        </Button>
                    </form>
                </div>
            </div>
            <p className='text-center pt-5 text-sm lg:text-base'>{t("copyRight")}</p>
        </div>
    );
};

export default Footer;
