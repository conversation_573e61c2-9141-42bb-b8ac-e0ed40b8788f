import ProductAndLightCard from "@/components/molecules/Product&LightCard"
import { useScreenSize } from "@/context/ScreenSizeContext"
import { lightingData } from "@/data/homePage"
import { useTranslations } from "next-intl"


const Lighting = () => {

    const t = useTranslations("Common")
    const { isMobile } = useScreenSize();

    return (
        <section id="lighting" className="text-black md:bg-secondary px-4 sm:px-6 lg:px-8 pb-6 md:py-30 "
            style={{
                backgroundImage: isMobile ? '': `url(/images/LightingBackground.svg)`,
                backgroundSize: '25% auto',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'top left',
            }}
        >

            {/* Main Content */}
            <div className="mx-auto container flex flex-col">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-5 md:mb-20">{t("lights")}</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10">
                    {lightingData.map((card) => (
                        <ProductAndLightCard
                            id={card.id}
                            key={card.id}
                            image={card.image}
                            title={card.title}
                            imageHeight="sm"
                            isLightCard
                        />
                    ))}
                </div>
            </div>
        </section>
    )
}

export default Lighting