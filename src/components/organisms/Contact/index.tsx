import Accordion from '@/components/atoms/Accordion'
import { useScreenSize } from '@/context/ScreenSizeContext';
import { useTranslations } from 'next-intl'
import React, { useState } from 'react'

const FAQs = () => {
  const t = useTranslations("contact");
  const { isMobile } = useScreenSize();

  type accordionItemsType = {
    titleKey: string,
    contentKey: string,
  }

  const accordionItems: accordionItemsType[] = [
    { titleKey: "accordionData1", contentKey: "accordionContent1" },
    { titleKey: "accordionData2", contentKey: "accordionContent2" },
    { titleKey: "accordionData3", contentKey: "accordionContent3" },
    { titleKey: "accordionData4", contentKey: "accordionContent4" },
  ];

  // State to track the currently open accordion
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);

  const renderAccordionContent = (contentKey: string) => {
    const content = t.raw(contentKey);


    if (content.type === 'paragraph') {
      return <p className='text-xm max-lg:text-lg font-bold leading-normal'>{content.content}</p>;
    }

    if (content.type === 'mixed') {
      return (
        <div className='text-xm max-lg:text-lg font-bold leading-normal space-y-3'>
          <p>{content.paragraph}</p>
          <ul className='space-y-2'>
            {content.items.map((item: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-primary ml-2 mt-1">•</span>
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </div>
      );
    }

    return null;
  };

  const hundelOpenAccordionAction = (item: accordionItemsType) => {
    setOpenAccordion(openAccordion === item.titleKey ? null : item.titleKey)
  }

  return (
    <section className="text-white px-4 sm:px-6 lg:px-8 py-4 pb-0 md:py-30 md:min-h-screen relative md:pt-70" style={{
      backgroundImage: `url(/images/contactBackground.jpg)`,
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'bottom',
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 115, 103,1)',
        mixBlendMode: 'darken',
        zIndex: 1,
        pointerEvents: 'none',
      }} />
      <div className="mx-auto container flex flex-col lg:flex-row gap-1" style={{ position: 'relative', zIndex: 2 }}>
        <div className='lg:w-1/2 space-y-6'>
          <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-5 md:mb-10">{t("contactTitle")}</h1>
          {!isMobile && <p className='text-sm md:text-md lg:text-lg font-medium leading-relaxed space-y-4 mb-10'>
            {t("contactIntro")}
          </p>}
        </div>
        <div className='lg:w-1/2 space-y-6'>
          {accordionItems.map((item) => (
            <Accordion key={item.titleKey} title={t(item.titleKey)} isOpen={openAccordion === item.titleKey} onClick={() => hundelOpenAccordionAction(item)}>
              {renderAccordionContent(item.contentKey)}
            </Accordion>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQs
