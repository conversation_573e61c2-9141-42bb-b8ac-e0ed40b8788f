'use client'

import Dropdown from "@/components/atoms/Dropdown";
import { DropdownOption } from "@/lib/types";

type DropdownSectionProps = {
    municipalityOptions: DropdownOption[];
    planNameOptions: DropdownOption[];
    planNoOptions: DropdownOption[];
    parcelNoOptions: DropdownOption[];
    municipality: string;
    setMunicipality: (value: string) => void;
    planName: string;
    setPlanName: (value: string) => void;
    planNo: string;
    setPlanNo: (value: string) => void;
    parcelNo: string;
    setParcelNo: (value: string) => void;
};

const DropdownSection = ({
    municipalityOptions,
    planNameOptions,
    planNoOptions,
    parcelNoOptions,
    municipality,
    setMunicipality,
    planName,
    setPlanName,
    planNo,
    setPlanNo,
    parcelNo,
    setParcelNo
}: DropdownSectionProps) => {

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 my-6">
            <Dropdown
                label="البلدية"
                placeholder="--- اختر البلدية التابع لها---"
                options={municipalityOptions}
                value={municipality}
                onChange={setMunicipality}
                searchable={true}
            />

            <Dropdown
                label="الحي"
                placeholder="--- أختر الحي التابع له ---"
                options={planNameOptions}
                value={planName}
                onChange={setPlanName}
                searchable={true}
            />

            <Dropdown
                label="رقم المخطط"
                placeholder="--- اختر رقم المخطط ---"
                options={planNoOptions}
                value={planNo}
                onChange={setPlanNo}
                searchable={true}
            />

            <Dropdown
                label="رقم القطعة"
                placeholder="--- اختر رقم القطعة  ---"
                options={parcelNoOptions}
                value={parcelNo}
                onChange={setParcelNo}
                searchable={true}
            />
        </div>
    )
}

export default DropdownSection;
