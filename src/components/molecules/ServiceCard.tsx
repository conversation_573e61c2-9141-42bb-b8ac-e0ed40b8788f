import { ServiceCardProps } from "@/lib/types";
import { Button } from "../atoms/Button";
import Image from "next/image";
import Link from "next/link";




function ServiceCard({
  image,
  title,
  description,
  buttonText,
  onButtonClick,
  className = '',
  imageHeight = 'md',
  contentPadding = 'md',
  link = ''
}: ServiceCardProps) {
  // Define image height classes
  const imageHeightClasses = {
    sm: 'h-32',
    md: 'h-48',
    lg: 'h-56',
    xl: 'h-64',
    auto: 'h-auto min-h-[120px]'
  };

  // Define content padding classes
  const contentPaddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  return (
    // a tag is used istead of Link tag to make sure that the ArcGIS CDN is fully loaded before navigating the the map page
    <a href={link}>
      <div className={`overflow-hidden bg-white rounded-[20px] shadow-[0px_1px_4px_0px_rgba(0,0,0,0.12)] outline-1 outline-offset-[-1px] outline-neutral-200
        max-w-md mx-auto ${className}`}>
        {/* Image */}
        <div className={`relative ${imageHeightClasses[imageHeight]} overflow-hidden rounded-lg`}>
          <Image
            src={image}
            alt={title}
            width={100}
            height={100}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Content - flexible height based on content */}
        <div className={`${contentPaddingClasses[contentPadding]} flex flex-col h-auto`}>
          {/* Title */}
          <h2 className="text-sm md:text-xl lg:text-2xl font-bold text-black mb-4 flex-shrink-0">
            {title}
          </h2>

          {/* Description - grows with content */}
          <p className={`text-black text-[10px] md:text-base font-normal ${buttonText ? 'mb-6 ' : ''} text-right leading-3 md:leading-snug flex-grow`}>
            {description}
          </p>

          {/* Optional Button */}
          {buttonText && (
            <div className="flex-shrink-0">
              <Button variant="outline2" onClick={onButtonClick}>
                <span>{buttonText}</span>
              </Button>
            </div>
          )}
        </div>
      </div>
    </a>
  );
}

export default ServiceCard;
