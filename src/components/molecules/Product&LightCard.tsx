import { ProductAndLightCardProps } from "@/lib/types";
import Image from "next/image";


const ProductAndLightCard = ({ image, title, className = '', imageHeight = 'md' ,isLightCard = false
}: ProductAndLightCardProps) => {
    const imageHeightClasses = {
        sm: 'h-32',
        md: 'h-48',
        lg: 'h-56',
        xl: 'h-64',
        auto: 'h-auto min-h-[120px]'
    };
    return (
        <div className={`overflow-hidden bg-white rounded-xl shadow-[0px_1px_4px_0px_rgba(0,0,0,0.15)] outline-1 outline-offset-[-1px] outline-gray-200
            ${className} max-w-full`}>
            {/* Image */}
            <div className={`relative ${imageHeightClasses[imageHeight]}`}>
                <Image
                    src={image}
                    alt={title}
                    width={100}
                    height={100}
                    className={`w-full h-full ${isLightCard ? ' object-cover ' : 'object-contain'}`}
                />
            </div>
            {/* Content */}
            <div className={`p-6 flex flex-col h-auto ${isLightCard ? '' : 'pb-1 border-t-1 border-t-[#E4E4E4]'}`}>
                {/* Title */}
                <h2 className="text-base text-center text-black mb-4 flex-shrink-0">
                    {title}
                </h2>
            </div>
        </div>
    )
}

export default ProductAndLightCard
