"use client";

import React, { useState, useRef, useEffect } from "react";
import { useScreenSize } from "@/context/ScreenSizeContext";

type ResponsiveCarouselGridProps<T> = {
    data: T[];
    renderCard: (item: T) => React.ReactNode;
    autoSlideInterval?: number;
    minSwipeDistance?: number;
    gridCols?: string;
    className?: string;
};

export default function ResponsiveCarouselGrid<T>({
    data,
    renderCard,
    autoSlideInterval = 5000,
    minSwipeDistance = 50,
    gridCols = "md:grid-cols-2 lg:grid-cols-3",
    className = "",
}: ResponsiveCarouselGridProps<T>) {
    const { isMobile } = useScreenSize();
    const [activeIndex, setActiveIndex] = useState(1);
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    const goToPrevious = () => {
        setActiveIndex((prev) => (prev === 0 ? data.length - 1 : prev - 1));
    };

    const goToNext = () => {
        setActiveIndex((prev) => (prev === data.length - 1 ? 0 : prev + 1));
    };

    const onTouchStart = (e: React.TouchEvent) => {
        setTouchEnd(0);
        setTouchStart(e.targetTouches[0].clientX);
    };

    const onTouchMove = (e: React.TouchEvent) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };

    const onTouchEnd = () => {
        if (!touchStart || !touchEnd) return;
        const distance = touchStart - touchEnd;
        if (distance > minSwipeDistance) goToNext();
        if (distance < -minSwipeDistance) goToPrevious();
    };

    useEffect(() => {
        if (isMobile) {
            const interval = setInterval(goToNext, autoSlideInterval);
            return () => clearInterval(interval);
        }
    }, [isMobile, autoSlideInterval]);

    return (
        <div className={`mx-auto container flex flex-col ${className}`}>
            {isMobile ? (
                <div
                    ref={containerRef}
                    className="relative w-full h-[300px] md:h-[400px] overflow-hidden"
                    onTouchStart={onTouchStart}
                    onTouchMove={onTouchMove}
                    onTouchEnd={onTouchEnd}
                >
                    <div className="flex items-center justify-around w-full h-full">
                        {data.map((item, index) => {
                            const position = index - activeIndex;
                            const normalized =
                                position < -2
                                    ? position + data.length
                                    : position > 2
                                        ? position - data.length
                                        : position;

                            const getStyles = () => {
                                switch (normalized) {
                                    case -2:
                                        return "absolute transform -translate-x-[180%] scale-60 z-10";
                                    case -1:
                                        return "absolute transform -translate-x-[85%] scale-65 z-20";
                                    case 0:
                                        return "absolute transform translate-x-0 scale-90 z-30";
                                    case 1:
                                        return "absolute transform translate-x-[85%] scale-65 z-20";
                                    case 2:
                                        return "absolute transform translate-x-[180%] scale-60 z-10";
                                    default:
                                        return "hidden";
                                }
                            };

                            return (
                                <div
                                    key={index}
                                    className={`transition-all duration-500 ease-in-out w-[60%] opacity-100 ${getStyles()}`}
                                >
                                    {renderCard(item)}
                                </div>
                            );
                        })}
                    </div>
                </div>
            ) : (
                <div className={`grid grid-cols-1 ${gridCols} gap-10`}>
                    {data.map((item, i) => (
                        <React.Fragment key={i}>{renderCard(item)}</React.Fragment>
                    ))}
                </div>
            )}
        </div>
    );
}
