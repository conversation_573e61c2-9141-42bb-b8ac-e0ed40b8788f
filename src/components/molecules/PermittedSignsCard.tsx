
type PermittedSignsProps = {
  heading: string;
  title?: string;
  description?: string;
  pdfHighlight?: boolean;
}

const PermittedSigns = ({ heading, title, description, pdfHighlight = false }: PermittedSignsProps) => {
  const Content = (
    <>
      <h4 className={`text-sm ${!pdfHighlight ? 'md:text-md lg:text-xl' : ''} font-bold`}>{heading}</h4>
      <span className={`text-sm ${!pdfHighlight ? 'md:text-md lg:text-xl' : ''} leading-7 font-semibold text-primary`}>{title}</span>
      <p className="leading-snug text-xs md:text-base">{description}</p>
    </>
  );

  return (
    <div className={`perm-sign-card flex flex-col justify-start items-start gap-3 overflow-hidden rounded-[20px] shadow-[0px_1px_4px_0px_rgba(0,0,0,0.12)] outline-1 outline-offset-[-1px] outline-neutral-200 ${pdfHighlight ? 'p-0' : 'pt-5 pb-7 p-3'}`}>
      {pdfHighlight ? (
        <div className="pdf-border w-full h-full rounded-md border-2 border-gray p-3 flex flex-col">
          {Content}
        </div>
      ) : (
        Content
      )}
    </div>
  )
}

export default PermittedSigns
