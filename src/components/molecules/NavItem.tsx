import { NavigationItem } from "@/lib/types";
import { Button } from "../atoms/Button";


const NavItem = ({ id, label, onClick, className, isMobile, disabled, isActive }: NavigationItem) => {
    // console.log(`is mobile ${isMobile}`)
    return (
        <>
            <button
                onClick={onClick}
                disabled={disabled}
                className={` show-at-800 hidden
                w-[95%] py-3 px-6 text-center text-white font-semibold text-xl
                transition-all duration-300 cursor-pointer bg-[#747474cb]'
                ${isActive ? 'bg-[#01413A]' : 'bg-transparent'}
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`} >
                {label}
            </button>
            <Button variant="outline3" key={id} onClick={onClick} className={className }>
                <span>{label}</span>
            </Button>
        </>
    )
}

export default NavItem