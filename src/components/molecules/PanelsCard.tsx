import { useTranslations } from 'next-intl';
import Image from 'next/image';

type PanelsCardProps = {
    cardNumber: string;
    title: string;
    description: string;
    purpose: string;
    generalRules: string[];
    colors: string[];
    lighting: string[];
    className?: string;
    imageURL: string;
}

const PanelsCard = ({
    cardNumber,
    title,
    description,
    purpose,
    generalRules,
    colors,
    lighting,
    className = "",
    imageURL
}: PanelsCardProps) => {

    const t = useTranslations("Common");

    return (
        <div className='relative w-full h-[91%] container'>
            <div className={`mx-auto h-full flex flex-col ${className}`}>
                {/* Header */}
                <div className="flex flex-row lg:flex-row-reverse justify-between items-baseline mb-4">
                    <div className="text-base md:text-xl lg:text-2xl font-bold text-primary leading-loose">
                        {cardNumber}
                    </div>
                    <h1 className="text-base md:text-xl lg:text-2xl font-bold text-primary text-end lg:text-start flex-1">
                        {title}
                    </h1>
                </div>

                <div className='overflow-y-auto scrollbar-hide flex-1 max-w-[574px]'>
                    {/* Description */}
                    <div className="mb-4 ">
                        <p className="text-zinc-800 leading-normal font-medium text-base">
                            <span className="text-teal-700 text-lg font-semibold mb-2">{t("description")}</span>{description}
                        </p>
                    </div>

                    {/* Purpose */}
                    <div className="mb-4 ">
                        <p className="text-zinc-800 leading-normal font-medium text-base">
                            <span className="text-teal-700 text-lg font-semibold mb-2">{t("purpose")}</span>{purpose}
                        </p>
                    </div>

                    {/* General Rules */}
                    <div className="mb-4 ">
                        <ul className="space-y-2">
                            {generalRules.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Colors */}
                    <div className="">
                        <h3 className="text-lg font-medium leading-normal text-teal-700">{t("colors")}</h3>
                        <ul className="space-y-2">
                            {colors.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Lighting */}
                    <div className="mb-4 ">
                        <h3 className="text-lg font-medium leading-normal text-teal-700">{t("lights")}</h3>
                        <ul className="space-y-2">
                            {lighting.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* The Image part this position can be adjusted */}
                    <div className="flex justify-end absolute -bottom-8 left-2">
                        <Image src={imageURL} alt="logo" width={200} height={200} />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default PanelsCard