import { useScreenSize } from '@/context/ScreenSizeContext';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

type PanelsCardProps = {
  cardNumber: string;
  title: string;
  description: string;
  purpose: string;
  generalRules: string[];
  colors: string[];
  lighting: string[];
  className?: string;
  imageURL: string;
}

const PanelsCard = ({
  cardNumber,
  title,
  description,
  className = "",
  imageURL
}: PanelsCardProps) => {

  const t = useTranslations("Common");
  const { isMobile } = useScreenSize();

  return (
    <div className={`relative w-full ${isMobile ? '' : 'h-[91%]'}  container`}>
      <div className={`mx-auto h-full flex flex-col ${className}`}>
        {/* Header */}
        <div className="flex flex-row lg:flex-row-reverse justify-between items-baseline mb-4 ">
          <div className="text-2xl font-bold text-primary leading-loose ml-3">
            {cardNumber}
          </div>
          <h1 className="text-2xl font-bold text-primary text-start flex-1">
            {title}
          </h1>
        </div>
        {isMobile && (<>
          < div className="absolute -left-10 -right-10 top-12 border-b border-[#D3D3D3]" />
        </>)}

        <div className='overflow-y-auto scrollbar-hide flex-1 max-w-[574px]'>
          {/* Description */}
          <div className="mb-4 ">
            <p className="text-zinc-800 leading-normal font-bold md:font-medium text-base">
              <span className="text-teal-700 text-lg font-semibold mb-2">{t("description")}</span>{description}
            </p>
          </div>

          {/* The Image part this position can be adjusted */}
          <div className={`lg:absolute lg:bottom-[10%] lg:left-[20%]`}>
            <Image src={imageURL} alt="logo" width={500} height={500} />
          </div>
        </div>
      </div>
    </div >
  );
}

export default PanelsCard
