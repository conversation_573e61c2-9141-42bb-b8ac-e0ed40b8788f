import { useEffect, useRef } from "react";
import { useTranslations } from 'next-intl';
import Image from 'next/image';

type PanelModalProps = {
    isOpen: boolean;
    onClose: () => void;
    generalRules: string[];
    colors: string[];
    lighting: string[];
    imageUrl: string;
    title: string;
};

const PanelModal = ({ isOpen = false, onClose, imageUrl, title, generalRules, colors, lighting }: PanelModalProps) => {

    const modalRef = useRef<HTMLDivElement>(null);
    const t = useTranslations("Common");

    // Close modal when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        // Add event listener if modal is open
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Don't render if not open
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-transparent bg-opacity-20 backdrop-blur-sm ">
            <div
                ref={modalRef}
                className="bg-white rounded-2xl shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)] p-10 max-w-3xl h-[75vh] max-h-[90vh] overflow-auto relative flex flex-col scrollbar-hide"
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <h1 className="text-base md:text-xl lg:text-2xl font-bold text-primary text-end lg:text-start mb-4">
                    {title}
                </h1>
                <div className='overflow-y-auto scrollbar-hide flex-1 max-w-[574px]'>
                    {/* Description */}
                    <div className="mb-4 ">
                        <p className="text-zinc-800 leading-normal font-medium text-base">
                            <span className="text-teal-700 text-lg font-semibold mb-2">{t("description")}</span>اللوحة المتدلية من المظلة تشير إلى لوحة معلقة بالكامل تحت المظلة وعمدانية على البناء الذي يغلق عليه المظلة.
                        </p>
                    </div>

                    {/* Purpose */}
                    <div className="mb-4 ">
                        <p className="text-zinc-800 leading-normal font-medium text-base">
                            <span className="text-teal-700 text-lg font-semibold mb-2">{t("purpose")}</span>من المفترض أن تقدم معلومات للمشاة وراكبي الدراجات الذين يسيرون أو يركبون على الواجهة، وذلك تكون الحجم محدود وغير موجه لحركة مرور السيارات.
                        </p>
                    </div>

                    {/* General Rules */}
                    <div className="mb-4 ">
                        <ul className="space-y-2">
                            {generalRules.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Colors */}
                    <div className="">
                        <h3 className="text-lg font-medium leading-normal text-teal-700">{t("colors")}</h3>
                        <ul className="space-y-2">
                            {colors.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Lighting */}
                    <div className="mb-4 ">
                        <h3 className="text-lg font-medium leading-normal text-teal-700">{t("lights")}</h3>
                        <ul className="space-y-2">
                            {lighting.map((rule, index) => (
                                <li key={index} className="text-zinc-800 flex items-start leading-normal font-medium text-base">
                                    <span className="text-teal-700 ml-2 mt-1">•</span>
                                    <span>{rule}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* The Image part this position can be adjusted */}
                    <div className="flex justify-center absolute left-1/3">
                        <Image src={imageUrl} alt="logo" width={200} height={200} />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PanelModal;
