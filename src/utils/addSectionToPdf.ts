import jsPDF from "jspdf";

export const addSectionToPdf = (pdf: jsPDF, canvas: HTMLCanvasElement, opts?: { marginXmm?: number }) => {
  const marginX = Math.max(0, opts?.marginXmm ?? 0);
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const drawWidth = Math.max(0.1, pageWidth - marginX * 2);

  const imgHeight = (canvas.height * drawWidth) / canvas.width;
  const imgData = canvas.toDataURL('image/png');

  let heightLeft = imgHeight;
  let position = 0;

  // First page image
  pdf.addImage(imgData, 'PNG', marginX, position, drawWidth, imgHeight, undefined, 'FAST');
  // Draw border on top for current page
  pdf.rect(1, 1, pageWidth - 2, pageHeight - 2);
  heightLeft -= pageHeight;

  // Overflow pages
  while (heightLeft > 0) {
    position = heightLeft - imgHeight;
    pdf.addPage();
    pdf.addImage(imgData, 'PNG', marginX, position, drawWidth, imgHeight, undefined, 'FAST');
    // Ensure border is above the content on each new page
    const pw = pdf.internal.pageSize.getWidth();
    const ph = pdf.internal.pageSize.getHeight();
    pdf.rect(1, 1, pw - 2, ph - 2);
    heightLeft -= pageHeight;
  }
};

