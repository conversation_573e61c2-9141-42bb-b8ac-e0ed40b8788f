export const smoothScrollTo = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
        });
    }
};

export const getActiveSection = (sections: string[]): string => {
    const scrollPosition = window.scrollY;
    const offset = 100; // Header height offset

    // Check each section from last to first (bottom to top)
    for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i]);
        if (section) {
            const rect = section.getBoundingClientRect();
            const sectionTop = rect.top + scrollPosition;

            // If we've scrolled past this section's top, it's the active one
            if (scrollPosition + offset >= sectionTop) {
                return sections[i];
            }
        }
    }

    return sections[0] || '';
};
