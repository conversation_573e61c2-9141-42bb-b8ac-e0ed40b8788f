'use client'

import { useState, useEffect, useCallback } from 'react';
import { getActiveSection } from '@/utils/scrollUtils';

export const useActiveSection = (sections: string[]) => {
    const [activeSection, setActiveSection] = useState<string>('');

    const handleScroll = useCallback(() => {
        const active = getActiveSection(sections);
        setActiveSection(prev => {
            if (prev !== active) {
                return active;
            }
            return prev;
        });
    }, [sections]);

    useEffect(() => {
        // Set initial active section
        handleScroll();

        // Add scroll listener with throttling
        let timeoutId: NodeJS.Timeout;
        const throttledScroll = () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(handleScroll, 50); // 50ms throttle
        };

        window.addEventListener('scroll', throttledScroll, { passive: true });

        return () => {
            window.removeEventListener('scroll', throttledScroll);
            clearTimeout(timeoutId);
        };
    }, [handleScroll]);

    return activeSection;
};
