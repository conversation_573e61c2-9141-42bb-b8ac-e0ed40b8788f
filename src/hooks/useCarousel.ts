import { useState, useEffect, useRef, useCallback } from 'react';

interface UseCarouselOptions {
    items: any[];
    autoSlideInterval?: number;
    minSwipeDistance?: number;
    getVisibleCardsCount?: () => number;
    defaultVisibleCards?: number;
}

interface UseCarouselReturn {
    // Refs
    sliderContentRef: React.RefObject<HTMLDivElement | null>;

    // State
    isReady: boolean;
    cardWidth: number;
    currentIndex: number;
    visibleCards: number;
    isHovered: boolean;

    // Tripled items for infinite loop
    tripleItems: any[];

    // Navigation handlers
    handleNext: () => void;
    handlePrevious: () => void;
    goToSlide: (index: number) => void;
    getCurrentSlideIndex: () => number;

    // Touch handlers
    onTouchStart: (e: React.TouchEvent) => void;
    onTouchMove: (e: React.TouchEvent) => void;
    onTouchEnd: () => void;

    // Hover handlers
    setIsHovered: (hovered: boolean) => void;

    // Reset function for when items change
    reset: () => void;
}

export const useCarousel = ({
    items,
    autoSlideInterval = 5000,
    minSwipeDistance = 50,
    getVisibleCardsCount,
    defaultVisibleCards = 1,
}: UseCarouselOptions): UseCarouselReturn => {
    const sliderContentRef = useRef<HTMLDivElement>(null);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const [isReady, setIsReady] = useState(false);
    const [cardWidth, setCardWidth] = useState(0);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [visibleCards, setVisibleCards] = useState(defaultVisibleCards);
    const [isHovered, setIsHovered] = useState(false);

    // Touch swipe states
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);

    const tripleItems = [...items, ...items, ...items];

    const calculateDimensions = useCallback(() => {
        const content = sliderContentRef.current;
        if (!content) return;

        const container = content.parentElement;
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const containerW = containerRect.width;

        const cardsToShow = getVisibleCardsCount ? getVisibleCardsCount() : defaultVisibleCards;
        setVisibleCards(cardsToShow);

        const gap = cardsToShow === 1 ? 0 : 16;
        const actualCardWidth =
            cardsToShow === 1
                ? containerW
                : (containerW - gap * (cardsToShow - 1)) / cardsToShow;

        setCardWidth(actualCardWidth);
        setIsReady(true);

        const initialIndex = items.length;
        setCurrentIndex(initialIndex);

        requestAnimationFrame(() => {
            const initialPosition = initialIndex * (actualCardWidth + gap);
            content.style.transform = `translateX(${initialPosition}px)`;
        });
    }, [items.length, getVisibleCardsCount, defaultVisibleCards]);

    // Calculate dimensions on mount and resize
    useEffect(() => {
        calculateDimensions();
        window.addEventListener('resize', calculateDimensions);
        return () => window.removeEventListener('resize', calculateDimensions);
    }, [calculateDimensions]);

    // Navigation functions
    const handleNext = useCallback(() => {
        const content = sliderContentRef.current;
        if (!content || !isReady) return;

        const gap = visibleCards === 1 ? 0 : 16;
        let newIndex = currentIndex + 1;

        if (newIndex >= items.length * 2) {
            newIndex = items.length;
        }

        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    }, [currentIndex, cardWidth, visibleCards, items.length, isReady]);

    const handlePrevious = useCallback(() => {
        const content = sliderContentRef.current;
        if (!content || !isReady) return;

        const gap = visibleCards === 1 ? 0 : 16;
        let newIndex = currentIndex - 1;

        if (newIndex < items.length) {
            newIndex = items.length * 2 - 1;
        }

        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    }, [currentIndex, cardWidth, visibleCards, items.length, isReady]);

    const goToSlide = useCallback((index: number) => {
        const content = sliderContentRef.current;
        if (!content || !isReady) return;

        const gap = visibleCards === 1 ? 0 : 16;
        const newIndex = items.length + index;
        setCurrentIndex(newIndex);
        const newPosition = newIndex * (cardWidth + gap);
        content.style.transition = 'transform 0.5s ease-in-out';
        content.style.transform = `translateX(${newPosition}px)`;
    }, [cardWidth, visibleCards, items.length, isReady]);

    const getCurrentSlideIndex = useCallback(() => {
        return (currentIndex - items.length + items.length) % items.length;
    }, [currentIndex, items.length]);

    // Auto slide
    useEffect(() => {
        if (!isReady || autoSlideInterval === 0) return;

        const startAutoSlide = () => {
            intervalRef.current = setInterval(() => {
                if (!isHovered) {
                    handleNext();
                }
            }, autoSlideInterval);
        };

        startAutoSlide();

        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [isReady, isHovered, handleNext, autoSlideInterval]);

    // Touch swipe handlers
    const onTouchStart = (e: React.TouchEvent) => {
        setTouchEnd(0);
        setTouchStart(e.targetTouches[0].clientX);
    };

    const onTouchMove = (e: React.TouchEvent) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };

    const onTouchEnd = () => {
        if (!touchStart || !touchEnd) return;
        const distance = touchStart - touchEnd;

        if (distance > minSwipeDistance) {
            handlePrevious();
        }

        if (distance < -minSwipeDistance) {
            handleNext();
        }
    };

    // Reset function for when items change
    const reset = useCallback(() => {
        setIsReady(false);
        setCurrentIndex(0);
        calculateDimensions();
    }, [calculateDimensions]);

    return {
        sliderContentRef,
        isReady,
        cardWidth,
        currentIndex,
        visibleCards,
        isHovered,
        tripleItems,
        handleNext,
        handlePrevious,
        goToSlide,
        getCurrentSlideIndex,
        onTouchStart,
        onTouchMove,
        onTouchEnd,
        setIsHovered,
        reset,
    };
};