//Shared TypeScript types
export type NavigationItem = {
  id: string;
  label: string;
  onClick: () => void;
  className?: string;
  isMobile?: boolean;
  isActive?: boolean;
  disabled?: boolean;
};

export type DropdownOption = {
  value: string;
  label: string;
};

export type NavigationProps = {
  items: NavigationItem[];
  activeSection: string;
  onNavigate: (sectionId: string) => void;
  className?: string;
  disabled?: boolean;
}

export type MobileMenuProps = {
  isOpen: boolean;
  onToggle: () => void;
  items: NavigationItem[];
  activeSection: string;
  onNavigate: (sectionId: string) => void;
  disabled?: boolean;
}

type ServiceCardBaseProps = {
  id: number;
  image: string;
  title: string;
  classification?: string;
  description: string;
  className?: string;
  imageHeight?: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
  contentPadding?: 'sm' | 'md' | 'lg';
  link?: string;
  intro?: { title?: string, description?: string },
  permittedSignsData?: PermittedSignsData[];
  exampleImages?: ExampleImage[];
  productsData?: ProductAndLightCardProps[];
  lightingDataUrban?: ProductAndLightCardProps[];
}
type ServiceCardWithButton = ServiceCardBaseProps & {
  buttonText: string;
  onButtonClick: () => void;
};

type ServiceCardWithoutButton = ServiceCardBaseProps & {
  buttonText?: undefined;
  onButtonClick?: undefined;
};

export type ServiceCardProps = ServiceCardWithButton | ServiceCardWithoutButton;


export type ProductAndLightCardProps = {
  id?: number;
  image: string;
  title: string;
  imageHeight?: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
  className?: string;
  isLightCard?: boolean;
}


export type ExampleImage = {
  src: string
  alt: string
  width: number
  height: number
}

export type PermittedSignsData = {
  heading: string;
  title?: string;
  description?: string;
}




export type MapPanelsCardProps = {
  title: string;
  className?: string;
  isSelected?: boolean;
  onToggleSelect?: () => void;
}

export type TableColumn = {
  key: string;
  label: string;
  className?: string;
}

export type TableRow = {
  [key: string]: string | number | React.ReactNode;
}

export type TableProps = {
  columns: TableColumn[];
  data: TableRow[];
  className?: string;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
}

export type Feature = {
  NewClass: string;
  OBJECTID: number;
  Municapility: number;
  PLAN_NAME: string;
  PLAN_NO: number;
  PARCELNO: string;
  PLAN_TYPE: number;
  [key: string]: any; // optional, for extra keys
};

export type SinglePanelsData = {
  id: number;
  cardNumber: string;
  title: string;
  description: string;
  purpose: string;
  generalRules: string[];
  standardSpecs?: Record<string, string>;
  colors: string[];
  lighting: string[];
  imageURL: string;
  regulations?: RegulationGroup[];
}

export type SelectedPanelDetailsProps = {
  activePanel: SinglePanelsData | null;
  panels: string[];
  pdfMode?: boolean;
  refs: {
    1: React.RefObject<HTMLDivElement | null>,
    2: React.RefObject<HTMLDivElement | null>
  }
}

// Panel regulations types
export type RegulationItem = {
  imgSrc: string;
  description: string;
}

export type RegulationEntry = {
  description?: string;
  notAllowed?: RegulationItem[];
  allowed?: RegulationItem[];
}

export type RegulationMap = Record<string, RegulationEntry>;

export type RegulationGroup = {
  name: string; // e.g., group title, sometimes 'noTitle'
  regulation: RegulationMap;
}
